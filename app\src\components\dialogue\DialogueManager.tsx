import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Trash2, Edit, Volume2, Play, Pause, Download } from 'lucide-react';

interface DialogueEntry {
    id: string;
    subject_id: string;
    subject_name?: string;
    dialogue: string;
    tone: string;
    emotion?: string;
    pace?: string;
    volume?: string;
    accent?: string;
    notes?: string;
}

interface Subject {
    id: string;
    name: string;
    description: string;
}

interface DialogueManagerProps {
    dialogueEntries: DialogueEntry[];
    subjects: Subject[];
    onDialogueChange: (entries: DialogueEntry[]) => void;
}

const TONE_OPTIONS = [
    'neutral', 'happy', 'sad', 'angry', 'excited', 'calm', 'nervous', 
    'confident', 'mysterious', 'playful', 'serious', 'sarcastic'
];

const EMOTION_OPTIONS = [
    'neutral', 'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust',
    'contempt', 'pride', 'shame', 'guilt', 'envy', 'gratitude', 'hope'
];

const PACE_OPTIONS = [
    'very slow', 'slow', 'normal', 'fast', 'very fast', 'variable'
];

const VOLUME_OPTIONS = [
    'whisper', 'quiet', 'normal', 'loud', 'shouting'
];

const ACCENT_OPTIONS = [
    'neutral', 'american', 'british', 'australian', 'irish', 'scottish',
    'southern', 'new york', 'california', 'texas', 'boston'
];

export const DialogueManager: React.FC<DialogueManagerProps> = ({
    dialogueEntries,
    subjects,
    onDialogueChange
}) => {
    const [editingEntry, setEditingEntry] = useState<DialogueEntry | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const createNewEntry = (): DialogueEntry => ({
        id: crypto.randomUUID(),
        subject_id: subjects[0]?.id || '',
        dialogue: '',
        tone: 'neutral',
        emotion: 'neutral',
        pace: 'normal',
        volume: 'normal',
        accent: 'neutral',
        notes: ''
    });

    const handleAddEntry = () => {
        setEditingEntry(createNewEntry());
        setIsDialogOpen(true);
    };

    const handleEditEntry = (entry: DialogueEntry) => {
        setEditingEntry({ ...entry });
        setIsDialogOpen(true);
    };

    const handleSaveEntry = () => {
        if (!editingEntry) return;

        const updatedEntries = dialogueEntries.some(e => e.id === editingEntry.id)
            ? dialogueEntries.map(e => e.id === editingEntry.id ? editingEntry : e)
            : [...dialogueEntries, editingEntry];

        onDialogueChange(updatedEntries);
        setIsDialogOpen(false);
        setEditingEntry(null);
    };

    const handleDeleteEntry = (id: string) => {
        onDialogueChange(dialogueEntries.filter(e => e.id !== id));
    };

    const getSubjectName = (subjectId: string) => {
        return subjects.find(s => s.id === subjectId)?.name || 'Unknown Subject';
    };

    const handlePlayDialogue = (entry: DialogueEntry) => {
        // Text-to-speech functionality
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(entry.dialogue);
            
            // Configure voice settings based on entry properties
            utterance.rate = entry.pace === 'very slow' ? 0.5 : 
                           entry.pace === 'slow' ? 0.7 :
                           entry.pace === 'fast' ? 1.3 :
                           entry.pace === 'very fast' ? 1.5 : 1.0;
            
            utterance.volume = entry.volume === 'whisper' ? 0.3 :
                              entry.volume === 'quiet' ? 0.6 :
                              entry.volume === 'loud' ? 0.9 :
                              entry.volume === 'shouting' ? 1.0 : 0.8;
            
            utterance.pitch = entry.emotion === 'happy' ? 1.2 :
                             entry.emotion === 'sad' ? 0.8 :
                             entry.emotion === 'angry' ? 0.9 :
                             entry.emotion === 'excited' ? 1.3 : 1.0;

            speechSynthesis.speak(utterance);
        }
    };

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle>Dialogue & Vocal Performance</CardTitle>
                    <Button onClick={handleAddEntry} size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Dialogue
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                {dialogueEntries.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                        <Volume2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No dialogue entries yet. Add some dialogue to bring your scene to life!</p>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {dialogueEntries.map((entry, index) => (
                            <div key={entry.id} className="border rounded-lg p-4 space-y-2">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Badge variant="outline">
                                            {getSubjectName(entry.subject_id)}
                                        </Badge>
                                        <Badge variant="secondary">{entry.tone}</Badge>
                                        {entry.emotion && entry.emotion !== 'neutral' && (
                                            <Badge variant="secondary">{entry.emotion}</Badge>
                                        )}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handlePlayDialogue(entry)}
                                        >
                                            <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleEditEntry(entry)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleDeleteEntry(entry.id)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                <p className="text-sm bg-muted p-2 rounded italic">
                                    "{entry.dialogue}"
                                </p>
                                {entry.notes && (
                                    <p className="text-xs text-muted-foreground">
                                        Notes: {entry.notes}
                                    </p>
                                )}
                            </div>
                        ))}
                    </div>
                )}

                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>
                                {editingEntry?.id && dialogueEntries.some(e => e.id === editingEntry.id) 
                                    ? 'Edit Dialogue Entry' 
                                    : 'Add Dialogue Entry'
                                }
                            </DialogTitle>
                        </DialogHeader>
                        
                        {editingEntry && (
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="subject">Character/Subject</Label>
                                    <Select
                                        value={editingEntry.subject_id}
                                        onValueChange={(value) => 
                                            setEditingEntry({ ...editingEntry, subject_id: value })
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select character" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {subjects.map(subject => (
                                                <SelectItem key={subject.id} value={subject.id}>
                                                    {subject.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Label htmlFor="dialogue">Dialogue Text</Label>
                                    <Textarea
                                        id="dialogue"
                                        placeholder="Enter the dialogue text..."
                                        value={editingEntry.dialogue}
                                        onChange={(e) => 
                                            setEditingEntry({ ...editingEntry, dialogue: e.target.value })
                                        }
                                        rows={3}
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="tone">Tone</Label>
                                        <Select
                                            value={editingEntry.tone}
                                            onValueChange={(value) => 
                                                setEditingEntry({ ...editingEntry, tone: value })
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {TONE_OPTIONS.map(tone => (
                                                    <SelectItem key={tone} value={tone}>
                                                        {tone.charAt(0).toUpperCase() + tone.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="emotion">Emotion</Label>
                                        <Select
                                            value={editingEntry.emotion}
                                            onValueChange={(value) => 
                                                setEditingEntry({ ...editingEntry, emotion: value })
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {EMOTION_OPTIONS.map(emotion => (
                                                    <SelectItem key={emotion} value={emotion}>
                                                        {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div className="grid grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="pace">Pace</Label>
                                        <Select
                                            value={editingEntry.pace}
                                            onValueChange={(value) => 
                                                setEditingEntry({ ...editingEntry, pace: value })
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {PACE_OPTIONS.map(pace => (
                                                    <SelectItem key={pace} value={pace}>
                                                        {pace.charAt(0).toUpperCase() + pace.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="volume">Volume</Label>
                                        <Select
                                            value={editingEntry.volume}
                                            onValueChange={(value) => 
                                                setEditingEntry({ ...editingEntry, volume: value })
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {VOLUME_OPTIONS.map(volume => (
                                                    <SelectItem key={volume} value={volume}>
                                                        {volume.charAt(0).toUpperCase() + volume.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="accent">Accent</Label>
                                        <Select
                                            value={editingEntry.accent}
                                            onValueChange={(value) => 
                                                setEditingEntry({ ...editingEntry, accent: value })
                                            }
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {ACCENT_OPTIONS.map(accent => (
                                                    <SelectItem key={accent} value={accent}>
                                                        {accent.charAt(0).toUpperCase() + accent.slice(1)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="notes">Director's Notes</Label>
                                    <Textarea
                                        id="notes"
                                        placeholder="Additional notes for the voice actor or director..."
                                        value={editingEntry.notes}
                                        onChange={(e) => 
                                            setEditingEntry({ ...editingEntry, notes: e.target.value })
                                        }
                                        rows={2}
                                    />
                                </div>

                                <div className="flex justify-between pt-4">
                                    <Button
                                        variant="outline"
                                        onClick={() => handlePlayDialogue(editingEntry)}
                                        disabled={!editingEntry.dialogue}
                                    >
                                        <Play className="h-4 w-4 mr-2" />
                                        Preview
                                    </Button>
                                    
                                    <div className="space-x-2">
                                        <Button
                                            variant="outline"
                                            onClick={() => setIsDialogOpen(false)}
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            onClick={handleSaveEntry}
                                            disabled={!editingEntry.dialogue || !editingEntry.subject_id}
                                        >
                                            Save
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
            </CardContent>
        </Card>
    );
};
