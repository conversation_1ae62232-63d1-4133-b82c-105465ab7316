import { useCallback, useEffect, useState, useRef } from "react";

interface GoogleUserDetails {
    id: string;
    email: string;
    name: string;
    picture?: string;
    verified_email: boolean;
}

interface GoogleTokenResponse {
    access_token: string;
    token_type: string;
    expires_in: number;
    id_token?: string;
    refresh_token?: string;
}

interface GoogleAuthState {
    isLoggedIn: boolean;
    userDetails: GoogleUserDetails | null;
    authLoading: boolean;
    token: string | null;
    login: () => Promise<void>;
    logout: () => void;
}

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
const GOOGLE_REDIRECT_URI = import.meta.env.VITE_GOOGLE_REDIRECT_URI || window.location.origin;

const STORAGE_KEYS = {
    ACCESS_TOKEN: "google_access_token",
    REFRESH_TOKEN: "google_refresh_token",
    TOKEN_EXPIRY: "google_token_expiry",
    USER_DETAILS: "google_user_details",
};

const storage = {
    get: (key: string) => localStorage.getItem(key),
    set: (key: string, value: string) => localStorage.setItem(key, value),
    remove: (key: string) => localStorage.removeItem(key),
    
    getAccessToken: () => localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN),
    getRefreshToken: () => localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
    getTokenExpiry: () => {
        const expiry = localStorage.getItem(STORAGE_KEYS.TOKEN_EXPIRY);
        return expiry ? parseInt(expiry) : null;
    },
    getUserDetails: () => {
        const details = localStorage.getItem(STORAGE_KEYS.USER_DETAILS);
        return details ? JSON.parse(details) : null;
    },
    
    setTokens: (accessToken: string, expiresIn: number, refreshToken?: string) => {
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
        localStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRY, (Date.now() + (expiresIn * 1000)).toString());
        if (refreshToken) {
            localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        }
    },
    
    setUserDetails: (userDetails: GoogleUserDetails) => {
        localStorage.setItem(STORAGE_KEYS.USER_DETAILS, JSON.stringify(userDetails));
    },
    
    clearAuth: () => {
        localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.TOKEN_EXPIRY);
        localStorage.removeItem(STORAGE_KEYS.USER_DETAILS);
    }
};

const generateRandomString = (length: number): string => {
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~";
    return Array.from({ length }, () => charset.charAt(Math.floor(Math.random() * charset.length))).join("");
};

const createCodeChallenge = async (codeVerifier: string): Promise<string> => {
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const digest = await crypto.subtle.digest('SHA-256', data);
    const base64String = btoa(String.fromCharCode(...new Uint8Array(digest)));
    return base64String.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
};

export function useGoogleAuth(): GoogleAuthState {
    const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
    const [userDetails, setUserDetails] = useState<GoogleUserDetails | null>(null);
    const [authLoading, setAuthLoading] = useState<boolean>(true);
    
    const api = {
        exchangeCodeForToken: async (code: string, codeVerifier: string): Promise<GoogleTokenResponse> => {
            const response = await fetch('https://oauth2.googleapis.com/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    client_id: GOOGLE_CLIENT_ID,
                    client_secret: '', // For public clients, this can be empty
                    code,
                    grant_type: 'authorization_code',
                    redirect_uri: GOOGLE_REDIRECT_URI,
                    code_verifier: codeVerifier,
                }),
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Token exchange failed: ${response.status} - ${errorText}`);
            }

            return await response.json();
        },

        refreshAccessToken: async (refreshToken: string): Promise<GoogleTokenResponse> => {
            const response = await fetch('https://oauth2.googleapis.com/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    client_id: GOOGLE_CLIENT_ID,
                    client_secret: '',
                    refresh_token: refreshToken,
                    grant_type: 'refresh_token',
                }),
            });

            if (!response.ok) {
                throw new Error(`Token refresh failed: ${response.status}`);
            }

            return await response.json();
        },

        getUserInfo: async (accessToken: string): Promise<GoogleUserDetails> => {
            const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to get user info: ${response.status}`);
            }

            return await response.json();
        },

        revokeToken: async (token: string): Promise<void> => {
            await fetch(`https://oauth2.googleapis.com/revoke?token=${token}`, {
                method: 'POST',
            });
        }
    };

    const auth = {
        validateAndSetUser: async (accessToken: string) => {
            try {
                const userInfo = await api.getUserInfo(accessToken);
                setUserDetails(userInfo);
                setIsLoggedIn(true);
                storage.setUserDetails(userInfo);
            } catch (error) {
                console.error('Failed to validate user:', error);
                throw error;
            }
        },

        exchangeCodeForToken: async (code: string) => {
            const codeVerifier = storage.get('google_code_verifier');
            
            if (!codeVerifier) {
                throw new Error("Missing code verifier");
            }

            try {
                const tokens = await api.exchangeCodeForToken(code, codeVerifier);
                storage.setTokens(tokens.access_token, tokens.expires_in, tokens.refresh_token);
                await auth.validateAndSetUser(tokens.access_token);
                storage.remove('google_code_verifier');
            } catch (error) {
                console.error('Token exchange failed:', error);
                throw error;
            }
        },

        refreshToken: async () => {
            const refreshToken = storage.getRefreshToken();
            
            if (!refreshToken) {
                throw new Error("No refresh token available");
            }

            try {
                const tokens = await api.refreshAccessToken(refreshToken);
                storage.setTokens(tokens.access_token, tokens.expires_in, tokens.refresh_token || refreshToken);
                await auth.validateAndSetUser(tokens.access_token);
            } catch (error) {
                console.error('Token refresh failed:', error);
                storage.clearAuth();
                setIsLoggedIn(false);
                setUserDetails(null);
                throw error;
            }
        },

        initialize: async () => {
            try {
                const accessToken = storage.getAccessToken();
                const tokenExpiry = storage.getTokenExpiry();
                const cachedUserDetails = storage.getUserDetails();
                
                if (accessToken && tokenExpiry && tokenExpiry > Date.now()) {
                    if (cachedUserDetails) {
                        setUserDetails(cachedUserDetails);
                        setIsLoggedIn(true);
                    } else {
                        await auth.validateAndSetUser(accessToken);
                    }
                } else if (storage.getRefreshToken()) {
                    await auth.refreshToken();
                } else {
                    // Check for authorization code in URL
                    const urlParams = new URLSearchParams(window.location.search);
                    const code = urlParams.get("code");

                    if (code) {
                        await auth.exchangeCodeForToken(code);
                        // Clean up URL
                        window.history.replaceState({}, document.title, window.location.pathname);
                    }
                }
            } catch (error) {
                console.error('Auth initialization failed:', error);
                setIsLoggedIn(false);
                setUserDetails(null);
            }
        }
    };

    const login = useCallback(async () => {
        if (!GOOGLE_CLIENT_ID) {
            console.error('Google Client ID not configured');
            return;
        }

        try {
            const codeVerifier = generateRandomString(128);
            const codeChallenge = await createCodeChallenge(codeVerifier);
            
            storage.set('google_code_verifier', codeVerifier);

            const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
            authUrl.searchParams.append('client_id', GOOGLE_CLIENT_ID);
            authUrl.searchParams.append('redirect_uri', GOOGLE_REDIRECT_URI);
            authUrl.searchParams.append('response_type', 'code');
            authUrl.searchParams.append('scope', 'openid email profile');
            authUrl.searchParams.append('code_challenge', codeChallenge);
            authUrl.searchParams.append('code_challenge_method', 'S256');
            authUrl.searchParams.append('access_type', 'offline');
            authUrl.searchParams.append('prompt', 'consent');

            window.location.href = authUrl.toString();
        } catch (error) {
            console.error('Login failed:', error);
        }
    }, []);

    const logout = useCallback(async () => {
        try {
            const accessToken = storage.getAccessToken();
            if (accessToken) {
                await api.revokeToken(accessToken);
            }
        } catch (error) {
            console.error('Token revocation failed:', error);
        } finally {
            storage.clearAuth();
            setIsLoggedIn(false);
            setUserDetails(null);
        }
    }, []);

    const isInitializing = useRef(false);
    
    useEffect(() => {
        if (isInitializing.current) return;
        isInitializing.current = true;
        setAuthLoading(true);

        auth.initialize().finally(() => {
            setAuthLoading(false);
            isInitializing.current = false;
        });
    }, []);

    return {
        isLoggedIn,
        userDetails,
        authLoading,
        login,
        logout,
        token: storage.getAccessToken()
    };
}
