from solar import Table, ColumnDetails
from typing import Optional, List, Dict
from datetime import datetime
import uuid

class Scene(Table):
    __tablename__ = "scenes"
    
    id: uuid.UUID = ColumnDetails(default_factory=uuid.uuid4, primary_key=True)
    project_id: uuid.UUID  # Reference to project
    
    scene_number: int
    scene_title: Optional[str] = None
    
    # Scene Elements
    action: Optional[str] = None
    environment: Optional[str] = None
    
    # Subject assignments for this scene (subject IDs)
    scene_subjects: List[str] = ColumnDetails(default_factory=list)  # List of subject UUIDs
    
    # Dialogue & Vocal Performance
    dialogue_entries: List[Dict] = ColumnDetails(default_factory=list)  # [{"subject_id": "uuid", "dialogue": "text", "tone": "neutral"}]
    
    # Cinematography
    composition: Optional[str] = None
    angle: Optional[str] = None
    camera_movements: List[str] = ColumnDetails(default_factory=list)
    
    # Visual Style (can override project defaults)
    visual_aesthetic: Optional[str] = None
    lighting_design: Optional[str] = None
    
    # Audio
    sound_design: Optional[str] = None
    
    # Advanced Controls
    negative_prompt: Optional[str] = None
    seed: Optional[int] = None
    
    # Screenplay integration
    screenplay_content: Optional[str] = None  # Raw screenplay text for this scene
    
    # Generated output
    generated_prompt: Optional[str] = None
    
    created_at: datetime = ColumnDetails(default_factory=datetime.now)
    last_updated: datetime = ColumnDetails(default_factory=datetime.now)