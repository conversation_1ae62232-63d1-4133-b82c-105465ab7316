import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Copy, Upload, Shuffle } from 'lucide-react';
import { promptGeneratorCreatePromptSession } from '@/lib/sdk';
import type { PromptSession } from '@/lib/sdk';
import { DialogueManager } from './dialogue/DialogueManager';
import { VocalPerformanceControls } from './dialogue/VocalPerformanceControls';
import { AudioWaveform } from './dialogue/AudioWaveform';
import { ScreenplayEditor } from './screenplay/ScreenplayEditor';
import { SceneConverter } from './screenplay/SceneConverter';
import type { ParsedScreenplay, ParsedScene } from '@/utils/screenplayParser';
import { ApiKeyManager } from './llm/ApiKeyManager';
import { ModelSelector } from './llm/ModelSelector';
import { PromptTemplates } from './llm/PromptTemplates';

interface DialogueEntry {
  id: string;
  subject_id: string;
  subject_name?: string;
  dialogue: string;
  tone: string;
  emotion?: string;
  pace?: string;
  volume?: string;
  accent?: string;
  notes?: string;
}

interface Subject {
  id: string;
  name: string;
  description: string;
}

const PromptArchitect: React.FC = () => {
  const [formData, setFormData] = useState({
    subjects: '',
    action: '',
    environment: '',
    composition: '',
    angle: '',
    camera_movements: [] as string[],
    visual_aesthetic: '',
    lighting_design: '',
    sound_design: '',
    negative_prompt: '',
    seed: ''
  });

  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [dialogueEntries, setDialogueEntries] = useState<DialogueEntry[]>([]);
  const [selectedDialogue, setSelectedDialogue] = useState<string>('');
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [screenplay, setScreenplay] = useState<ParsedScreenplay | null>(null);
  const [selectedScene, setSelectedScene] = useState<ParsedScene | null>(null);
  const [showScreenplayEditor, setShowScreenplayEditor] = useState(false);
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  // Mock subjects data - in real app, this would come from API
  const [subjects] = useState<Subject[]>([
    { id: '1', name: 'Detective Morgan', description: 'A grizzled detective in a trench coat' },
    { id: '2', name: 'The Suspect', description: 'A mysterious figure in shadows' },
    { id: '3', name: 'Narrator', description: 'Omniscient voice-over narrator' }
  ]);

  const compositionOptions = [
    'Wide Shot',
    'Extreme Wide Shot',
    'Medium Shot',
    'Close-up',
    'Extreme Close-up',
    'Over-the-Shoulder Shot'
  ];

  const angleOptions = [
    'Eye-Level',
    'High-Angle',
    'Low-Angle',
    'Dutch Angle',
    "Bird's-Eye View"
  ];

  const cameraMovementOptions = [
    'Static Shot',
    'Pan (Left/Right)',
    'Tilt (Up/Down)',
    'Dolly (In/Out)',
    'Crane/Jib Shot',
    'Tracking/Follow Shot',
    'Handheld/Shaky Cam',
    'Drone Footage'
  ];

  const visualAestheticSuggestions = [
    'Hyperrealistic, 8K',
    'Cinematic, 35mm film',
    'Vintage 1980s VHS',
    'Gritty film noir',
    'Vibrant anime style',
    'Impressionistic watercolor',
    'Moody, atmospheric'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCameraMovementChange = (movement: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      camera_movements: checked
        ? [...prev.camera_movements, movement]
        : prev.camera_movements.filter(m => m !== movement)
    }));
  };

  const generateRandomSeed = () => {
    const randomSeed = Math.floor(Math.random() * 1000000);
    setFormData(prev => ({ ...prev, seed: randomSeed.toString() }));
  };

  const generatePrompt = async () => {
    setIsGenerating(true);
    try {
      const response = await promptGeneratorCreatePromptSession({
        body: {
          subjects: formData.subjects || null,
          action: formData.action || null,
          environment: formData.environment || null,
          composition: formData.composition || null,
          angle: formData.angle || null,
          camera_movements: formData.camera_movements.length > 0 ? formData.camera_movements : null,
          visual_aesthetic: formData.visual_aesthetic || null,
          lighting_design: formData.lighting_design || null,
          sound_design: formData.sound_design || null,
          negative_prompt: formData.negative_prompt || null,
          seed: formData.seed ? parseInt(formData.seed) : null
        }
      });

      if (response.data) {
        setGeneratedPrompt(response.data.generated_prompt || '');
      }
    } catch (error) {
      console.error('Error generating prompt:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedPrompt);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleDialogueSelect = (dialogue: string) => {
    setSelectedDialogue(dialogue);
  };

  const handleScreenplayChange = (parsedScreenplay: ParsedScreenplay) => {
    setScreenplay(parsedScreenplay);
  };

  const handleSceneSelect = (scene: ParsedScene) => {
    setSelectedScene(scene);
    // Auto-populate form data from scene
    const sceneData = {
      subjects: scene.characters.join(', '),
      action: scene.action.join(' '),
      environment: `${scene.location} - ${scene.timeOfDay}`,
      // Keep existing values for other fields
      composition: formData.composition,
      angle: formData.angle,
      camera_movements: formData.camera_movements,
      visual_aesthetic: formData.visual_aesthetic,
      lighting_design: formData.lighting_design,
      sound_design: formData.sound_design,
      negative_prompt: formData.negative_prompt,
      seed: formData.seed
    };
    setFormData(sceneData);

    // Convert dialogue entries
    const sceneDialogue = scene.dialogue.map((d, index) => ({
      id: `scene-${scene.sceneNumber}-${index}`,
      subject_id: d.character,
      subject_name: d.character,
      dialogue: d.text,
      tone: 'neutral',
      emotion: 'neutral',
      pace: 'normal',
      volume: 'normal',
      accent: 'neutral',
      notes: ''
    }));
    setDialogueEntries(sceneDialogue);
  };

  const handleSceneConvert = (promptData: any) => {
    // Apply converted data to form
    setFormData(prev => ({
      ...prev,
      ...promptData
    }));

    if (promptData.dialogue_entries) {
      setDialogueEntries(promptData.dialogue_entries);
    }
  };

  const handleApiKeyChange = (provider: string, apiKey: string) => {
    setApiKeys(prev => ({ ...prev, [provider]: apiKey }));
  };

  const handleModelChange = (model: any) => {
    setSelectedModel(model);
  };

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template);
  };

  const handleTemplateApply = (prompt: string) => {
    setGeneratedPrompt(prompt);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-2">Veo 3 Prompt Architect</h1>
        <p className="text-gray-600">Generate structured prompts for Google's Veo video generation model</p>
      </div>

      {/* LLM Configuration Section */}
      <ApiKeyManager onApiKeyChange={handleApiKeyChange} />

      <ModelSelector
        selectedModel={selectedModel?.id}
        onModelChange={handleModelChange}
      />

      <PromptTemplates
        onTemplateSelect={handleTemplateSelect}
        onTemplateApply={handleTemplateApply}
      />

      {/* Section 1: Core Scene Elements */}
      <Card>
        <CardHeader>
          <CardTitle>1. Core Scene Elements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="subjects">Subject(s)</Label>
            <Input
              id="subjects"
              placeholder="e.g., A grizzled detective in a trench coat"
              value={formData.subjects}
              onChange={(e) => handleInputChange('subjects', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="action">Action</Label>
            <Input
              id="action"
              placeholder="e.g., sprinting through a crowded market"
              value={formData.action}
              onChange={(e) => handleInputChange('action', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="environment">Environment & Setting</Label>
            <Textarea
              id="environment"
              placeholder="e.g., A desolate, rain-slicked alleyway in a cyberpunk city at midnight"
              value={formData.environment}
              onChange={(e) => handleInputChange('environment', e.target.value)}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Section 2: Cinematic Language & Visual Style */}
      <Card>
        <CardHeader>
          <CardTitle>2. Cinematic Language & Visual Style</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="composition">Composition</Label>
              <Select value={formData.composition} onValueChange={(value) => handleInputChange('composition', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select composition" />
                </SelectTrigger>
                <SelectContent>
                  {compositionOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="angle">Angle</Label>
              <Select value={formData.angle} onValueChange={(value) => handleInputChange('angle', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select angle" />
                </SelectTrigger>
                <SelectContent>
                  {angleOptions.map(option => (
                    <SelectItem key={option} value={option}>{option}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label>Camera Movement</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
              {cameraMovementOptions.map(movement => (
                <div key={movement} className="flex items-center space-x-2">
                  <Checkbox
                    id={movement}
                    checked={formData.camera_movements.includes(movement)}
                    onCheckedChange={(checked) => handleCameraMovementChange(movement, checked as boolean)}
                  />
                  <Label htmlFor={movement} className="text-sm">{movement}</Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <Label htmlFor="visual_aesthetic">Visual Aesthetic</Label>
            <Input
              id="visual_aesthetic"
              placeholder="e.g., Hyperrealistic, 8K or Cinematic, 35mm film"
              value={formData.visual_aesthetic}
              onChange={(e) => handleInputChange('visual_aesthetic', e.target.value)}
              list="aesthetic-suggestions"
            />
            <datalist id="aesthetic-suggestions">
              {visualAestheticSuggestions.map(suggestion => (
                <option key={suggestion} value={suggestion} />
              ))}
            </datalist>
          </div>

          <div>
            <Label htmlFor="lighting_design">Lighting Design</Label>
            <Input
              id="lighting_design"
              placeholder="e.g., Dramatic, high-contrast chiaroscuro"
              value={formData.lighting_design}
              onChange={(e) => handleInputChange('lighting_design', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Section 3: Audio Landscape */}
      <Card>
        <CardHeader>
          <CardTitle>3. Audio Landscape (Optional)</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="sound_design">Sound Design</Label>
            <Textarea
              id="sound_design"
              placeholder="e.g., Sounds of a bustling futuristic city with flying vehicles, rainfall, and a melancholic synth-wave soundtrack"
              value={formData.sound_design}
              onChange={(e) => handleInputChange('sound_design', e.target.value)}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Section 3.5: Screenplay Integration */}
      <ScreenplayEditor
        onSceneSelect={handleSceneSelect}
        onScreenplayChange={handleScreenplayChange}
      />

      {/* Scene Converter */}
      {selectedScene && (
        <SceneConverter
          scene={selectedScene}
          onConvert={handleSceneConvert}
        />
      )}

      {/* Section 3.6: Dialogue & Vocal Performance */}
      <DialogueManager
        dialogueEntries={dialogueEntries}
        subjects={subjects}
        onDialogueChange={setDialogueEntries}
      />

      {/* Vocal Performance Controls */}
      {selectedDialogue && (
        <VocalPerformanceControls
          text={selectedDialogue}
          onAudioGenerated={setAudioBlob}
        />
      )}

      {/* Audio Waveform */}
      {audioBlob && (
        <AudioWaveform
          audioBlob={audioBlob}
          title="Generated Audio"
        />
      )}

      {/* Section 4: Advanced Controls */}
      <Card>
        <CardHeader>
          <CardTitle>4. Advanced Controls & Consistency</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Character Reference Image</Label>
            <div className="flex items-center space-x-2 mt-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </Button>
              <span className="text-sm text-gray-500">Upload a character reference for consistency</span>
            </div>
          </div>

          <div>
            <Label htmlFor="negative_prompt">Negative Prompt</Label>
            <Input
              id="negative_prompt"
              placeholder="e.g., blurry, low-resolution, text, watermark"
              value={formData.negative_prompt}
              onChange={(e) => handleInputChange('negative_prompt', e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="seed">Seed</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="seed"
                type="number"
                placeholder="Leave blank for random"
                value={formData.seed}
                onChange={(e) => handleInputChange('seed', e.target.value)}
              />
              <Button type="button" variant="outline" size="sm" onClick={generateRandomSeed}>
                <Shuffle className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section 5: Prompt Generation & Output */}
      <Card>
        <CardHeader>
          <CardTitle>5. Prompt Generation & Output</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={generatePrompt}
            className="w-full"
            size="lg"
            disabled={isGenerating}
          >
            {isGenerating ? 'Generating Prompt...' : 'Generate Prompt'}
          </Button>

          {generatedPrompt && (
            <div className="space-y-2">
              <Label>Generated Prompt</Label>
              <div className="relative">
                <Textarea
                  value={generatedPrompt}
                  readOnly
                  rows={6}
                  className="pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={copyToClipboard}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PromptArchitect;