import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
    FileText, 
    Plus, 
    Edit, 
    Trash2, 
    Copy, 
    Star,
    Download,
    Upload,
    Wand2,
    Tag
} from 'lucide-react';

interface PromptTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    template: string;
    variables: string[];
    tags: string[];
    isDefault: boolean;
    createdAt: Date;
    usageCount: number;
}

interface PromptTemplatesProps {
    onTemplateSelect?: (template: PromptTemplate) => void;
    onTemplateApply?: (prompt: string) => void;
}

const DEFAULT_TEMPLATES: PromptTemplate[] = [
    {
        id: 'veo-basic',
        name: 'Veo Basic Scene',
        description: 'Basic template for Veo video generation',
        category: 'video',
        template: `Create a video of {{subjects}} {{action}} in {{environment}}. 

Shot composition: {{composition}}
Camera angle: {{angle}}
Visual style: {{visual_aesthetic}}
Lighting: {{lighting_design}}

{{#if sound_design}}Audio: {{sound_design}}{{/if}}
{{#if negative_prompt}}Avoid: {{negative_prompt}}{{/if}}`,
        variables: ['subjects', 'action', 'environment', 'composition', 'angle', 'visual_aesthetic', 'lighting_design', 'sound_design', 'negative_prompt'],
        tags: ['video', 'veo', 'basic'],
        isDefault: true,
        createdAt: new Date(),
        usageCount: 0
    },
    {
        id: 'cinematic-detailed',
        name: 'Cinematic Detailed',
        description: 'Detailed cinematic prompt with advanced controls',
        category: 'video',
        template: `Professional cinematic video: {{subjects}} {{action}} in {{environment}}.

CINEMATOGRAPHY:
- Shot: {{composition}}
- Angle: {{angle}}
- Movement: {{camera_movements}}

VISUAL DESIGN:
- Aesthetic: {{visual_aesthetic}}
- Lighting: {{lighting_design}}
- Mood: {{mood}}

AUDIO LANDSCAPE:
{{sound_design}}

{{#if dialogue_entries}}
DIALOGUE:
{{#each dialogue_entries}}
- {{subject_name}}: "{{dialogue}}" ({{tone}})
{{/each}}
{{/if}}

TECHNICAL SPECS:
- Duration: {{duration}} seconds
- Quality: 4K, cinematic
{{#if seed}}
- Seed: {{seed}}
{{/if}}

{{#if negative_prompt}}
AVOID: {{negative_prompt}}
{{/if}}`,
        variables: ['subjects', 'action', 'environment', 'composition', 'angle', 'camera_movements', 'visual_aesthetic', 'lighting_design', 'mood', 'sound_design', 'dialogue_entries', 'duration', 'seed', 'negative_prompt'],
        tags: ['video', 'cinematic', 'detailed', 'professional'],
        isDefault: true,
        createdAt: new Date(),
        usageCount: 0
    },
    {
        id: 'screenplay-adaptation',
        name: 'Screenplay Adaptation',
        description: 'Convert screenplay scenes to video prompts',
        category: 'screenplay',
        template: `SCENE ADAPTATION: {{scene_title}}

SETTING: {{environment}}
TIME: {{time_of_day}}

CHARACTERS:
{{#each characters}}
- {{this}}
{{/each}}

ACTION:
{{action}}

{{#if dialogue_entries}}
KEY DIALOGUE:
{{#each dialogue_entries}}
{{subject_name}}: "{{dialogue}}"
{{/each}}
{{/if}}

VISUAL TREATMENT:
- Style: {{visual_aesthetic}}
- Camera: {{angle}}, {{composition}}
- Lighting: {{lighting_design}}

AUDIO:
{{sound_design}}

DIRECTION NOTES:
Focus on {{mood}} atmosphere. Emphasize {{key_elements}}.

{{#if negative_prompt}}
AVOID: {{negative_prompt}}
{{/if}}`,
        variables: ['scene_title', 'environment', 'time_of_day', 'characters', 'action', 'dialogue_entries', 'visual_aesthetic', 'angle', 'composition', 'lighting_design', 'sound_design', 'mood', 'key_elements', 'negative_prompt'],
        tags: ['screenplay', 'adaptation', 'scene'],
        isDefault: true,
        createdAt: new Date(),
        usageCount: 0
    }
];

export const PromptTemplates: React.FC<PromptTemplatesProps> = ({
    onTemplateSelect,
    onTemplateApply
}) => {
    const [templates, setTemplates] = useState<PromptTemplate[]>(DEFAULT_TEMPLATES);
    const [selectedTemplate, setSelectedTemplate] = useState<PromptTemplate | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
    const [newTemplate, setNewTemplate] = useState({
        name: '',
        description: '',
        category: '',
        template: '',
        tags: ''
    });
    const [filterCategory, setFilterCategory] = useState('all');

    useEffect(() => {
        loadTemplates();
    }, []);

    const loadTemplates = () => {
        try {
            const stored = localStorage.getItem('prompt_templates');
            if (stored) {
                const storedTemplates = JSON.parse(stored).map((t: any) => ({
                    ...t,
                    createdAt: new Date(t.createdAt)
                }));
                setTemplates([...DEFAULT_TEMPLATES, ...storedTemplates]);
            }
        } catch (error) {
            console.error('Error loading templates:', error);
        }
    };

    const saveTemplates = (newTemplates: PromptTemplate[]) => {
        try {
            const customTemplates = newTemplates.filter(t => !t.isDefault);
            localStorage.setItem('prompt_templates', JSON.stringify(customTemplates));
            setTemplates(newTemplates);
        } catch (error) {
            console.error('Error saving templates:', error);
        }
    };

    const handleAddTemplate = () => {
        if (!newTemplate.name || !newTemplate.template) return;

        const template: PromptTemplate = {
            id: crypto.randomUUID(),
            name: newTemplate.name,
            description: newTemplate.description,
            category: newTemplate.category || 'custom',
            template: newTemplate.template,
            variables: extractVariables(newTemplate.template),
            tags: newTemplate.tags.split(',').map(t => t.trim()).filter(Boolean),
            isDefault: false,
            createdAt: new Date(),
            usageCount: 0
        };

        const updatedTemplates = [...templates, template];
        saveTemplates(updatedTemplates);
        
        setNewTemplate({ name: '', description: '', category: '', template: '', tags: '' });
        setIsDialogOpen(false);
    };

    const handleEditTemplate = (template: PromptTemplate) => {
        setEditingTemplate(template);
        setNewTemplate({
            name: template.name,
            description: template.description,
            category: template.category,
            template: template.template,
            tags: template.tags.join(', ')
        });
        setIsDialogOpen(true);
    };

    const handleUpdateTemplate = () => {
        if (!editingTemplate || !newTemplate.name || !newTemplate.template) return;

        const updatedTemplates = templates.map(t => 
            t.id === editingTemplate.id 
                ? {
                    ...t,
                    name: newTemplate.name,
                    description: newTemplate.description,
                    category: newTemplate.category || 'custom',
                    template: newTemplate.template,
                    variables: extractVariables(newTemplate.template),
                    tags: newTemplate.tags.split(',').map(t => t.trim()).filter(Boolean)
                }
                : t
        );
        
        saveTemplates(updatedTemplates);
        
        setNewTemplate({ name: '', description: '', category: '', template: '', tags: '' });
        setEditingTemplate(null);
        setIsDialogOpen(false);
    };

    const handleDeleteTemplate = (id: string) => {
        const updatedTemplates = templates.filter(t => t.id !== id);
        saveTemplates(updatedTemplates);
    };

    const handleSelectTemplate = (template: PromptTemplate) => {
        setSelectedTemplate(template);
        onTemplateSelect?.(template);
        
        // Increment usage count
        const updatedTemplates = templates.map(t => 
            t.id === template.id ? { ...t, usageCount: t.usageCount + 1 } : t
        );
        saveTemplates(updatedTemplates);
    };

    const extractVariables = (template: string): string[] => {
        const matches = template.match(/\{\{([^}]+)\}\}/g);
        if (!matches) return [];
        
        return Array.from(new Set(
            matches.map(match => match.replace(/\{\{|\}\}/g, '').split(' ')[0])
        ));
    };

    const categories = Array.from(new Set(templates.map(t => t.category)));
    const filteredTemplates = filterCategory === 'all' 
        ? templates 
        : templates.filter(t => t.category === filterCategory);

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Prompt Templates
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Select value={filterCategory} onValueChange={setFilterCategory}>
                            <SelectTrigger className="w-40">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Categories</SelectItem>
                                {categories.map(category => (
                                    <SelectItem key={category} value={category}>
                                        {category.charAt(0).toUpperCase() + category.slice(1)}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        
                        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                            <DialogTrigger asChild>
                                <Button size="sm">
                                    <Plus className="h-4 w-4 mr-2" />
                                    New Template
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                    <DialogTitle>
                                        {editingTemplate ? 'Edit Template' : 'Create New Template'}
                                    </DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="template-name">Name</Label>
                                            <Input
                                                id="template-name"
                                                placeholder="Template name"
                                                value={newTemplate.name}
                                                onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="template-category">Category</Label>
                                            <Input
                                                id="template-category"
                                                placeholder="e.g., video, screenplay"
                                                value={newTemplate.category}
                                                onChange={(e) => setNewTemplate(prev => ({ ...prev, category: e.target.value }))}
                                            />
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <Label htmlFor="template-description">Description</Label>
                                        <Input
                                            id="template-description"
                                            placeholder="Brief description of the template"
                                            value={newTemplate.description}
                                            onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                                        />
                                    </div>
                                    
                                    <div>
                                        <Label htmlFor="template-tags">Tags (comma-separated)</Label>
                                        <Input
                                            id="template-tags"
                                            placeholder="video, cinematic, basic"
                                            value={newTemplate.tags}
                                            onChange={(e) => setNewTemplate(prev => ({ ...prev, tags: e.target.value }))}
                                        />
                                    </div>
                                    
                                    <div>
                                        <Label htmlFor="template-content">Template Content</Label>
                                        <Textarea
                                            id="template-content"
                                            placeholder="Use {{variable}} for dynamic content..."
                                            value={newTemplate.template}
                                            onChange={(e) => setNewTemplate(prev => ({ ...prev, template: e.target.value }))}
                                            rows={10}
                                            className="font-mono text-sm"
                                        />
                                        <div className="text-xs text-muted-foreground mt-1">
                                            Variables found: {extractVariables(newTemplate.template).join(', ')}
                                        </div>
                                    </div>
                                    
                                    <div className="flex justify-end gap-2">
                                        <Button
                                            variant="outline"
                                            onClick={() => {
                                                setIsDialogOpen(false);
                                                setEditingTemplate(null);
                                                setNewTemplate({ name: '', description: '', category: '', template: '', tags: '' });
                                            }}
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            onClick={editingTemplate ? handleUpdateTemplate : handleAddTemplate}
                                            disabled={!newTemplate.name || !newTemplate.template}
                                        >
                                            {editingTemplate ? 'Update' : 'Create'} Template
                                        </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                {filteredTemplates.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                        <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No templates found</p>
                    </div>
                ) : (
                    filteredTemplates.map(template => (
                        <div 
                            key={template.id}
                            className={`border rounded-lg p-4 cursor-pointer transition-colors hover:bg-muted/50 ${
                                selectedTemplate?.id === template.id ? 'ring-2 ring-primary' : ''
                            }`}
                            onClick={() => handleSelectTemplate(template)}
                        >
                            <div className="flex items-start justify-between mb-2">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                        <h3 className="font-semibold">{template.name}</h3>
                                        <Badge variant="outline">{template.category}</Badge>
                                        {template.isDefault && (
                                            <Badge variant="secondary">
                                                <Star className="h-3 w-3 mr-1" />
                                                Default
                                            </Badge>
                                        )}
                                    </div>
                                    <p className="text-sm text-muted-foreground mb-2">
                                        {template.description}
                                    </p>
                                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                        <span>{template.variables.length} variables</span>
                                        <span>•</span>
                                        <span>Used {template.usageCount} times</span>
                                    </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigator.clipboard.writeText(template.template);
                                        }}
                                    >
                                        <Copy className="h-4 w-4" />
                                    </Button>
                                    {!template.isDefault && (
                                        <>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleEditTemplate(template);
                                                }}
                                            >
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteTemplate(template.id);
                                                }}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                            
                            <div className="flex flex-wrap gap-1 mb-2">
                                {template.tags.map(tag => (
                                    <Badge key={tag} variant="outline" className="text-xs">
                                        <Tag className="h-2 w-2 mr-1" />
                                        {tag}
                                    </Badge>
                                ))}
                            </div>
                            
                            <div className="text-xs bg-muted p-2 rounded font-mono">
                                {template.template.substring(0, 150)}
                                {template.template.length > 150 && '...'}
                            </div>
                        </div>
                    ))
                )}
            </CardContent>
        </Card>
    );
};
