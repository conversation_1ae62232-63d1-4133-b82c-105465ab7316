# Auto-generated by <PERSON><PERSON><PERSON>
##############################################################################
# Dependencies
##############################################################################


from fastapi import Depends, FastAPI, HTTPException, Request, status, Body, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import HTMLResponse, Response

from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, HTMLResponse, Response
from fastapi.exceptions import RequestValidationError
from fastapi.security import OAuth2PasswordBearer

import sys
import os
import asyncio
import logging
import traceback
import contextvars
import httpx
import jwt
import json
import requests
from pathlib import Path
import builtins

from datetime import datetime, date, time, timedelta
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Any, TypeVar, Awaitable, List, Optional, Dict, Union, Literal, Annotated, Tuple, Set
from functools import partial, wraps
from uuid import UUID
import uuid

from solar.access import User
from solar.media import MediaFile

from api.utils import get_swagger_ui_html
from api.models import TokenExchangeRequest, TokenResponse, TokenValidationRequest, LogoutResponse

OPENROUTER_API_KEY = os.environ.get("OPENROUTER_API_KEY")
ROUTER_BASE_URL = os.environ.get("ROUTER_BASE_URL")
SOLAR_APP_TOKEN_URL = f"{ROUTER_BASE_URL}/innerApp/oauth2/token"
SOLAR_APP_INTROSPECT_URL = f"{ROUTER_BASE_URL}/innerApp/oauth2/introspect"
REFRESH_TOKEN_COOKIE_NAME = "refresh_token"





from .models import BodyPromptGeneratorCreateScene, CreateSceneOutputSchema, BodyPromptGeneratorGetScene, GetSceneOutputSchema, BodyPromptGeneratorGetProjectScenes, GetProjectScenesOutputSchema, BodyPromptGeneratorUpdateScene, UpdateSceneOutputSchema, BodyProjectManagerCreateProject, CreateProjectOutputSchema, GetUserProjectsOutputSchema, BodyProjectManagerGetProject, GetProjectOutputSchema, BodyProjectManagerUpdateProject, UpdateProjectOutputSchema, BodyProjectManagerDeleteProject, DeleteProjectOutputSchema, BodySubjectManagerCreateSubject, CreateSubjectOutputSchema, BodySubjectManagerGetProjectSubjects, GetProjectSubjectsOutputSchema, BodySubjectManagerUpdateSubject, UpdateSubjectOutputSchema, BodySubjectManagerDeleteSubject, DeleteSubjectOutputSchema
from core import prompt_generator, project_manager, subject_manager


###############################################################################
# Logging Setup
###############################################################################
import sys
from loguru import logger
from pathlib import Path
from typing import TypeVar
import traceback

def format_record(record):
    fmt = "{level:<5} | {message}"
    if record["exception"] is not None:
        exc_type, exc_value, exc_traceback = record["exception"]        
        tb_lines = traceback.extract_tb(exc_traceback)
        if tb_lines:
            last_frame = tb_lines[-1]
            error_info = (
                f'\nFile "{last_frame.filename}", line {last_frame.lineno}, in {last_frame.name}\n'
                f'  {last_frame.line}\n'
                f'{exc_type.__name__}: {exc_value}'
            )
            record["message"] += error_info
        
        record["exception"] = None
    
    return fmt + "\n"

logger.remove()
logger.add(
    sys.stderr,
    level="DEBUG",
    format=format_record,
    colorize=True
)

Path("../logs").mkdir(exist_ok=True)
logger.add(
    "../logs/fast_api.log",
    rotation="50 MB",
    retention="10 days",
    level="DEBUG",
    format=format_record
)

# need this to capture print statements
class InterceptHandler:
    def write(self, msg):
        if msg.strip():
            logger.info(msg.strip())
    
    def flush(self):
        pass

sys.stdout = InterceptHandler()

T = TypeVar('T')



##############################################################################
# General App
##############################################################################

app = FastAPI(
    title="New app — 6/17 @ 1:34 PM",
    docs_url=None
)

###############################################################################
# Simple Request Logging Middleware
###############################################################################

@app.middleware("http")
async def log_requests(request: Request, call_next):
    request_id = str(uuid.uuid4())[:8]
    
    with logger.contextualize(request_id=request_id):
        start_time = datetime.utcnow()
        
        try:
            response = await call_next(request)
            process_time = (datetime.utcnow() - start_time).total_seconds()
            if "HEAD /docs" not in request.url.path:
              logger.info(f"{request.method} {request.url.path} ({response.status_code}) - {process_time:.3f}s")
            return response
        except Exception as e:
            process_time = (datetime.utcnow() - start_time).total_seconds()
            logger.exception(f"{request.method} {request.url.path} - Failed after {process_time:.3f}s")
            raise
            
###############################################################################
# Error Handler
###############################################################################
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception on {request.method} {request.url.path}: {exc}", exc_info=True)
    
    # In production, don't expose error details
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred"
        }
    )
    
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle pydantic validation errors"""
    logger.error(f"Validation error on {request.url.path}: {exc.errors()}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Validation Error",
            "message": "Invalid request parameters",
            "details": exc.errors()
        }
    )
    
@app.exception_handler(Exception)
async def handle_errors(request: Request, exc: Exception):
    logger.exception(f"Unhandled error: {type(exc).__name__}: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "message": str(exc)}
    )

@app.exception_handler(RequestValidationError)
async def handle_validation_errors(request: Request, exc: RequestValidationError):
    logger.warning(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={"error": "Validation failed", "details": exc.errors()}
    )

# We need to put a token endpoint here, but we're injecting the token,
# so we'll just put a mock endpoint here.
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/mockedTokenEndpoint/")
ENV = os.environ.get("ENV", "deployment")

def get_auth_origins():
    if ENV == "sandbox":
        origins = [
            os.environ.get("SANDBOX_FRONTEND_URL", ""),
            os.environ.get("SANDBOX_BACKEND_URL", ""),
        ]
    else:
        origins = [os.environ.get("PUBLIC_DOMAIN", "")]
    
    return [origin for origin in origins if origin]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

async def auth_cors_middleware(request: Request, call_next):
    if request.url.path.startswith("/api/auth"):
        auth_origins = get_auth_origins()
        origin = request.headers.get("origin", "")
        response = await call_next(request)
        
        # override the wildcard CORS settings with strict origin checking
        if origin in auth_origins:
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept"
            response.headers["Access-Control-Expose-Headers"] = "Set-Cookie"
        else:
            # unauthorized origins on auth routes, set CORS headers to blank or remove them
            response.headers["Access-Control-Allow-Origin"] = ""
            response.headers["Access-Control-Allow-Methods"] = ""
            response.headers["Access-Control-Allow-Headers"] = ""
            
        return response
    else:
        return await call_next(request)

# auth-specific middleware and logging middleware
app.middleware("http")(auth_cors_middleware)

# OPTIONS handler for auth endpoints
@app.options("/api/auth/{rest_of_path:path}", include_in_schema=False)
async def auth_options_handler(request: Request):
    auth_origins = get_auth_origins()
    origin = request.headers.get("origin", "")
    response = Response()
    
    if origin in auth_origins:
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept"
        response.headers["Access-Control-Max-Age"] = "3600"
    
    return response

@app.head("/docs", include_in_schema=False)
async def health_check():
    return {"status": "healthy"}
    
##############################################################################
# Synchronous Function Helpers
##############################################################################

thread_pool = ThreadPoolExecutor(max_workers=4)

async def run_sync_in_thread(func: Callable[..., Any], *args, **kwargs) -> Any:
    """Runs a synchronous function in a thread pool"""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(
        thread_pool,
        partial(func, *args, **kwargs)
    )


##############################################################################
# Custom Docs
##############################################################################

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " on Solar",
        swagger_ui_parameters={
            "persistAuthorization": False,
            "syntaxHighlight": {"theme": "obsidian"},
        }
    )

##############################################################################
# Auth Routes
##############################################################################

async def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        # Check if it's a Google OAuth token
        if await is_google_token(token):
            return await get_google_user(token)

        # Fallback to Solar platform authentication
        base_url = os.getenv("ROUTER_BASE_URL")
        if not base_url:
            raise HTTPException(status_code=500, detail="ROUTER_BASE_URL is not set, could not authenticate user")

        try:
            decoded_token = jwt.decode(token, options={"verify_signature": False})

            jti = decoded_token.get("jti")
            if not jti:
                raise HTTPException(status_code=401, detail="Invalid token format")

            exp = decoded_token.get("exp")
            if exp is not None and exp < datetime.utcnow().timestamp():
                raise HTTPException(status_code=401, detail="Token expired")

        except jwt.DecodeError:
            raise HTTPException(status_code=401, detail="Malformed token")

        token_url = f"{base_url}/innerApp/oauth2/introspect"
        async with httpx.AsyncClient(timeout=20.0) as client:
            response = await client.post(token_url, json={"token": jti, "token_type_hint": "access_token"})
            if response.status_code != 200:
                raise HTTPException(status_code=401, detail="Unauthorized")

            json_response = response.json()
            if not json_response.get("active", False):
                raise HTTPException(status_code=401, detail="Unauthorized")

            user_uuid = json_response.get("userUuid")
            email = json_response.get("email")
            if not user_uuid or not email:
                raise HTTPException(status_code=401, detail="Invalid user data")

            user = User(id=user_uuid, email=email)
            return user
    except HTTPException:
        raise
    except Exception as e:
        print(f"get_current_user failed with error: {type(e).__name__}")
        raise HTTPException(status_code=401, detail="Unauthorized")

async def is_google_token(token: str) -> bool:
    """Check if the token is a Google OAuth token by attempting to validate it with Google."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(
                f"https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={token}"
            )
            return response.status_code == 200
    except:
        return False

async def get_google_user(token: str) -> User:
    """Get user information from Google OAuth token."""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Get user info from Google
            response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {token}"}
            )

            if response.status_code != 200:
                raise HTTPException(status_code=401, detail="Invalid Google token")

            user_info = response.json()
            email = user_info.get("email")
            user_id = user_info.get("id")

            if not email or not user_id:
                raise HTTPException(status_code=401, detail="Invalid Google user data")

            # Create or get user with Google ID as UUID
            # For simplicity, we'll use the Google ID directly
            # In production, you might want to map this to your own user system
            user = User(id=user_id, email=email)
            return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Google authentication failed: {e}")
        raise HTTPException(status_code=401, detail="Google authentication failed")

def extract_domain(url):
    if not url:
        return None
    
    # Remove protocol
    if url.startswith("http://"):
        url = url[7:]
    elif url.startswith("https://"):
        url = url[8:]
    
    # Remove trailing slash
    if url.endswith("/"):
        url = url[:-1]
    
    return url

@app.post('/api/auth/token', response_model=TokenResponse, include_in_schema=False)
async def exchange_token(request: Request, body: TokenExchangeRequest = Body(...)):    
    try:
        params = body.model_dump(exclude_none=True)
        if params.get("grant_type") == REFRESH_TOKEN_COOKIE_NAME:
            refresh_token = request.cookies.get(REFRESH_TOKEN_COOKIE_NAME)
            if not refresh_token:
                return JSONResponse(
                    status_code=401,
                    content={
                        "status": "auth_required",
                        "message": "Authentication required",
                        "token_type": "bearer",
                        "access_token": "",
                        "expires_in": 0
                    }
                )

            params[REFRESH_TOKEN_COOKIE_NAME] = refresh_token
        
        if params.get("grant_type") == REFRESH_TOKEN_COOKIE_NAME and params.get(REFRESH_TOKEN_COOKIE_NAME):
            try:
                refresh_token = params[REFRESH_TOKEN_COOKIE_NAME]
                payload = jwt.decode(refresh_token, options={"verify_signature": False})
                
                if payload and payload.get("jti"):
                    params[REFRESH_TOKEN_COOKIE_NAME] = payload["jti"]
            except Exception as e:
                logger.warning("Error extracting JTI from refresh token")

        response = requests.post(
            SOLAR_APP_TOKEN_URL,
            json=params,
            headers={"Content-Type": "application/json", "Accept": "application/json"}
        )
        
        if not response.ok:
            return JSONResponse(
                    status_code=401,
                    content={
                        "status": "auth_required",
                        "message": "Authorization code invalid or expired",
                        "token_type": "bearer",
                        "access_token": "",
                        "expires_in": 0
                    }
                )
        
        tokens = response.json()
        
        # Safe null check for access token
        access_token = tokens.get("access_token")
        if not access_token:
            return HTTPException(status_code=401, detail="Received incomplete token data from server")
        
        token_response = TokenResponse(
            access_token=access_token,
            token_type=tokens.get("token_type", "bearer"),
            expires_in=tokens.get("expires_in", 3600)
        )
        
        content = token_response.model_dump()
        api_response = JSONResponse(content=content)
        
        # Safe null check for refresh token
        refresh_token_value = tokens.get(REFRESH_TOKEN_COOKIE_NAME)
        if refresh_token_value:
            if ENV == "sandbox" and not os.environ.get("SANDBOX_BACKEND_URL", None):
              return HTTPException(status_code=401, detail="Token exchange failed: sandbox frontend URL not set")
            
            if ENV == "deployment" and not os.environ.get("PUBLIC_DOMAIN", None):
              return HTTPException(status_code=401, detail="Token exchange failed: public domain not set")
            
            domain = None
            if ENV == "sandbox":
                domain = extract_domain(os.environ.get("SANDBOX_BACKEND_URL"))
            else:
                domain = extract_domain(os.environ.get("PUBLIC_DOMAIN"))

            api_response.set_cookie(
                key=REFRESH_TOKEN_COOKIE_NAME,
                value=refresh_token_value,
                httponly=True,
                secure=True,
                samesite="none" if ENV == "sandbox" else "strict",
                domain=domain,
                path="/api/auth"
            )
        
        return api_response
        
    except Exception as e:
        return HTTPException(status_code=401, detail=f"Token exchange failed: {str(e)}")


@app.post('/api/auth/logout', response_model=LogoutResponse, include_in_schema=False)
async def logout():
    response = JSONResponse(content={"success": True})
    
    if ENV == "sandbox" and not os.environ.get("SANDBOX_BACKEND_URL", None):
      return HTTPException(status_code=401, detail="Logout failed: sandbox frontend URL not set")
    
    if ENV == "deployment" and not os.environ.get("PUBLIC_DOMAIN", None):
      return HTTPException(status_code=401, detail="Logout failed: public domain not set")
    
    domain = None
    if ENV == "sandbox":
        domain = extract_domain(os.environ.get("SANDBOX_BACKEND_URL"))
    else:
        domain = extract_domain(os.environ.get("PUBLIC_DOMAIN"))
    
    response.delete_cookie(
        key=REFRESH_TOKEN_COOKIE_NAME,
        path="/api/auth",
        secure=True,
        httponly=True,
        samesite="none" if ENV == "sandbox" else "strict",
        domain=domain,
    )
    
    return response


##############################################################################
# Normal Routes
##############################################################################






@app.post('/api/prompt_generator/create_scene', response_model=CreateSceneOutputSchema, operation_id='prompt_generator_create_scene')
async def prompt_generator_create_scene(body: BodyPromptGeneratorCreateScene = Body(...), current_user: User = Depends(get_current_user)) -> CreateSceneOutputSchema:
    """
    Create a new scene in a project.
    """
    response = await run_sync_in_thread(prompt_generator.create_scene, user=current_user, project_id=body.project_id, scene_number=body.scene_number, scene_title=body.scene_title, action=body.action, environment=body.environment, scene_subjects=body.scene_subjects, dialogue_entries=body.dialogue_entries, composition=body.composition, angle=body.angle, camera_movements=body.camera_movements, visual_aesthetic=body.visual_aesthetic, lighting_design=body.lighting_design, sound_design=body.sound_design, negative_prompt=body.negative_prompt, seed=body.seed, screenplay_content=body.screenplay_content)
    return response
    
    




@app.post('/api/prompt_generator/get_scene', response_model=GetSceneOutputSchema, operation_id='prompt_generator_get_scene')
async def prompt_generator_get_scene(body: BodyPromptGeneratorGetScene = Body(...), current_user: User = Depends(get_current_user)) -> GetSceneOutputSchema:
    """
    Get a scene by its ID.
    """
    response = await run_sync_in_thread(prompt_generator.get_scene, user=current_user, scene_id=body.scene_id)
    return response
    
    




@app.post('/api/prompt_generator/get_project_scenes', response_model=GetProjectScenesOutputSchema, operation_id='prompt_generator_get_project_scenes')
async def prompt_generator_get_project_scenes(body: BodyPromptGeneratorGetProjectScenes = Body(...), current_user: User = Depends(get_current_user)) -> GetProjectScenesOutputSchema:
    """
    Get all scenes for a project.
    """
    response = await run_sync_in_thread(prompt_generator.get_project_scenes, user=current_user, project_id=body.project_id)
    return response
    
    




@app.post('/api/prompt_generator/update_scene', response_model=UpdateSceneOutputSchema, operation_id='prompt_generator_update_scene')
async def prompt_generator_update_scene(body: BodyPromptGeneratorUpdateScene = Body(...), current_user: User = Depends(get_current_user)) -> UpdateSceneOutputSchema:
    """
    Update an existing scene and regenerate the prompt.
    """
    response = await run_sync_in_thread(prompt_generator.update_scene, user=current_user, scene_id=body.scene_id, scene_title=body.scene_title, action=body.action, environment=body.environment, scene_subjects=body.scene_subjects, dialogue_entries=body.dialogue_entries, composition=body.composition, angle=body.angle, camera_movements=body.camera_movements, visual_aesthetic=body.visual_aesthetic, lighting_design=body.lighting_design, sound_design=body.sound_design, negative_prompt=body.negative_prompt, seed=body.seed, screenplay_content=body.screenplay_content)
    return response
    
    




@app.post('/api/project_manager/create_project', response_model=CreateProjectOutputSchema, operation_id='project_manager_create_project')
async def project_manager_create_project(body: BodyProjectManagerCreateProject = Body(...), current_user: User = Depends(get_current_user)) -> CreateProjectOutputSchema:
    """
    Create a new project for the authenticated user.
    """
    response = await run_sync_in_thread(project_manager.create_project, user=current_user, name=body.name, description=body.description)
    return response
    
    




@app.post('/api/project_manager/get_user_projects', response_model=GetUserProjectsOutputSchema, operation_id='project_manager_get_user_projects')
async def project_manager_get_user_projects(current_user: User = Depends(get_current_user)) -> GetUserProjectsOutputSchema:
    """
    Get all projects for the authenticated user.
    """
    response = await run_sync_in_thread(project_manager.get_user_projects, user=current_user)
    return response
    
    




@app.post('/api/project_manager/get_project', response_model=GetProjectOutputSchema, operation_id='project_manager_get_project')
async def project_manager_get_project(body: BodyProjectManagerGetProject = Body(...), current_user: User = Depends(get_current_user)) -> GetProjectOutputSchema:
    """
    Get a specific project owned by the authenticated user.
    """
    response = await run_sync_in_thread(project_manager.get_project, user=current_user, project_id=body.project_id)
    return response
    
    




@app.post('/api/project_manager/update_project', response_model=UpdateProjectOutputSchema, operation_id='project_manager_update_project')
async def project_manager_update_project(body: BodyProjectManagerUpdateProject = Body(...), current_user: User = Depends(get_current_user)) -> UpdateProjectOutputSchema:
    """
    Update an existing project.
    """
    response = await run_sync_in_thread(project_manager.update_project, user=current_user, project_id=body.project_id, name=body.name, description=body.description, global_visual_aesthetic=body.global_visual_aesthetic, global_lighting_design=body.global_lighting_design)
    return response
    
    




@app.post('/api/project_manager/delete_project', response_model=DeleteProjectOutputSchema, operation_id='project_manager_delete_project')
async def project_manager_delete_project(body: BodyProjectManagerDeleteProject = Body(...), current_user: User = Depends(get_current_user)) -> DeleteProjectOutputSchema:
    """
    Delete a project and all its associated scenes and subjects.
    """
    response = await run_sync_in_thread(project_manager.delete_project, user=current_user, project_id=body.project_id)
    return response
    
    




@app.post('/api/subject_manager/create_subject', response_model=CreateSubjectOutputSchema, operation_id='subject_manager_create_subject')
async def subject_manager_create_subject(body: BodySubjectManagerCreateSubject = Body(...), current_user: User = Depends(get_current_user)) -> CreateSubjectOutputSchema:
    """
    Create a new subject for a project.
    """
    response = await run_sync_in_thread(subject_manager.create_subject, user=current_user, project_id=body.project_id, name=body.name, description=body.description, character_reference_url=body.character_reference_url, is_primary_character=body.is_primary_character)
    return response
    
    




@app.post('/api/subject_manager/get_project_subjects', response_model=GetProjectSubjectsOutputSchema, operation_id='subject_manager_get_project_subjects')
async def subject_manager_get_project_subjects(body: BodySubjectManagerGetProjectSubjects = Body(...), current_user: User = Depends(get_current_user)) -> GetProjectSubjectsOutputSchema:
    """
    Get all subjects for a project owned by the user.
    """
    response = await run_sync_in_thread(subject_manager.get_project_subjects, user=current_user, project_id=body.project_id)
    return response
    
    




@app.post('/api/subject_manager/update_subject', response_model=UpdateSubjectOutputSchema, operation_id='subject_manager_update_subject')
async def subject_manager_update_subject(body: BodySubjectManagerUpdateSubject = Body(...), current_user: User = Depends(get_current_user)) -> UpdateSubjectOutputSchema:
    """
    Update an existing subject.
    """
    response = await run_sync_in_thread(subject_manager.update_subject, user=current_user, subject_id=body.subject_id, name=body.name, description=body.description, character_reference_url=body.character_reference_url, is_primary_character=body.is_primary_character)
    return response
    
    




@app.post('/api/subject_manager/delete_subject', response_model=DeleteSubjectOutputSchema, operation_id='subject_manager_delete_subject')
async def subject_manager_delete_subject(body: BodySubjectManagerDeleteSubject = Body(...), current_user: User = Depends(get_current_user)) -> DeleteSubjectOutputSchema:
    """
    Delete a subject.
    """
    response = await run_sync_in_thread(subject_manager.delete_subject, user=current_user, subject_id=body.subject_id)
    return response
    
    

