name: Create Release Branch

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  create-release-branch:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Important to fetch all history for branches and tags
          # Use a token with write permissions
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Get tag name
        id: get_tag
        run: echo "TAG_NAME=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: Create release branch
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          RELEASE_BRANCH="release/${TAG_NAME}"
          git checkout -b $RELEASE_BRANCH
          git push origin $RELEASE_BRANCH
        env:
          TAG_NAME: ${{ steps.get_tag.outputs.TAG_NAME }}