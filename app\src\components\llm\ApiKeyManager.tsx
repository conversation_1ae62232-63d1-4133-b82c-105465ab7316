import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
    Key, 
    Plus, 
    Trash2, 
    Edit, 
    Eye, 
    EyeOff, 
    Check, 
    X,
    Settings,
    Shield,
    AlertTriangle
} from 'lucide-react';

interface ApiKey {
    id: string;
    name: string;
    provider: string;
    key: string;
    isActive: boolean;
    createdAt: Date;
    lastUsed?: Date;
}

interface ApiKeyManagerProps {
    onApiKeyChange?: (provider: string, apiKey: string) => void;
}

const PROVIDERS = [
    { id: 'openrouter', name: 'OpenRouter', description: 'Access to multiple LLM models' },
    { id: 'openai', name: 'OpenAI', description: 'GPT models and DALL-E' },
    { id: 'anthropic', name: 'Anthropic', description: 'Claude models' },
    { id: 'google', name: 'Google AI', description: 'Gemini and PaLM models' },
    { id: 'cohere', name: 'Cohere', description: 'Command and Embed models' },
    { id: 'huggingface', name: 'Hugging Face', description: 'Open source models' },
    { id: 'replicate', name: 'Replicate', description: 'Various AI models' },
    { id: 'together', name: 'Together AI', description: 'Open source LLMs' }
];

export const ApiKeyManager: React.FC<ApiKeyManagerProps> = ({
    onApiKeyChange
}) => {
    const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingKey, setEditingKey] = useState<ApiKey | null>(null);
    const [showKey, setShowKey] = useState<Record<string, boolean>>({});
    const [newKey, setNewKey] = useState({
        name: '',
        provider: '',
        key: ''
    });

    useEffect(() => {
        loadApiKeys();
    }, []);

    const loadApiKeys = () => {
        try {
            const stored = localStorage.getItem('llm_api_keys');
            if (stored) {
                const keys = JSON.parse(stored).map((key: any) => ({
                    ...key,
                    createdAt: new Date(key.createdAt),
                    lastUsed: key.lastUsed ? new Date(key.lastUsed) : undefined
                }));
                setApiKeys(keys);
            }
        } catch (error) {
            console.error('Error loading API keys:', error);
        }
    };

    const saveApiKeys = (keys: ApiKey[]) => {
        try {
            localStorage.setItem('llm_api_keys', JSON.stringify(keys));
            setApiKeys(keys);
        } catch (error) {
            console.error('Error saving API keys:', error);
        }
    };

    const handleAddKey = () => {
        if (!newKey.name || !newKey.provider || !newKey.key) return;

        const apiKey: ApiKey = {
            id: crypto.randomUUID(),
            name: newKey.name,
            provider: newKey.provider,
            key: newKey.key,
            isActive: true,
            createdAt: new Date()
        };

        const updatedKeys = [...apiKeys, apiKey];
        saveApiKeys(updatedKeys);
        
        // Notify parent component
        onApiKeyChange?.(apiKey.provider, apiKey.key);
        
        // Reset form
        setNewKey({ name: '', provider: '', key: '' });
        setIsDialogOpen(false);
    };

    const handleEditKey = (key: ApiKey) => {
        setEditingKey(key);
        setNewKey({
            name: key.name,
            provider: key.provider,
            key: key.key
        });
        setIsDialogOpen(true);
    };

    const handleUpdateKey = () => {
        if (!editingKey || !newKey.name || !newKey.provider || !newKey.key) return;

        const updatedKeys = apiKeys.map(key => 
            key.id === editingKey.id 
                ? { ...key, name: newKey.name, provider: newKey.provider, key: newKey.key }
                : key
        );
        
        saveApiKeys(updatedKeys);
        
        // Notify parent component if this is the active key
        if (editingKey.isActive) {
            onApiKeyChange?.(newKey.provider, newKey.key);
        }
        
        // Reset form
        setNewKey({ name: '', provider: '', key: '' });
        setEditingKey(null);
        setIsDialogOpen(false);
    };

    const handleDeleteKey = (id: string) => {
        const updatedKeys = apiKeys.filter(key => key.id !== id);
        saveApiKeys(updatedKeys);
    };

    const handleToggleActive = (id: string) => {
        const updatedKeys = apiKeys.map(key => {
            if (key.id === id) {
                const newActiveState = !key.isActive;
                if (newActiveState) {
                    // Notify parent component
                    onApiKeyChange?.(key.provider, key.key);
                }
                return { ...key, isActive: newActiveState };
            }
            return { ...key, isActive: false }; // Deactivate others for same provider
        });
        
        saveApiKeys(updatedKeys);
    };

    const toggleShowKey = (id: string) => {
        setShowKey(prev => ({ ...prev, [id]: !prev[id] }));
    };

    const maskKey = (key: string) => {
        if (key.length <= 8) return '*'.repeat(key.length);
        return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
    };

    const getProviderName = (providerId: string) => {
        return PROVIDERS.find(p => p.id === providerId)?.name || providerId;
    };

    const getKeysByProvider = () => {
        return apiKeys.reduce((acc, key) => {
            if (!acc[key.provider]) acc[key.provider] = [];
            acc[key.provider].push(key);
            return acc;
        }, {} as Record<string, ApiKey[]>);
    };

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <Key className="h-5 w-5" />
                        API Key Management
                    </CardTitle>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button size="sm">
                                <Plus className="h-4 w-4 mr-2" />
                                Add API Key
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>
                                    {editingKey ? 'Edit API Key' : 'Add New API Key'}
                                </DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="key-name">Name</Label>
                                    <Input
                                        id="key-name"
                                        placeholder="e.g., My OpenAI Key"
                                        value={newKey.name}
                                        onChange={(e) => setNewKey(prev => ({ ...prev, name: e.target.value }))}
                                    />
                                </div>
                                
                                <div>
                                    <Label htmlFor="provider">Provider</Label>
                                    <select
                                        id="provider"
                                        value={newKey.provider}
                                        onChange={(e) => setNewKey(prev => ({ ...prev, provider: e.target.value }))}
                                        className="w-full p-2 border rounded-md"
                                    >
                                        <option value="">Select a provider</option>
                                        {PROVIDERS.map(provider => (
                                            <option key={provider.id} value={provider.id}>
                                                {provider.name} - {provider.description}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                
                                <div>
                                    <Label htmlFor="api-key">API Key</Label>
                                    <Input
                                        id="api-key"
                                        type="password"
                                        placeholder="Enter your API key"
                                        value={newKey.key}
                                        onChange={(e) => setNewKey(prev => ({ ...prev, key: e.target.value }))}
                                    />
                                </div>
                                
                                <div className="flex justify-end gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setIsDialogOpen(false);
                                            setEditingKey(null);
                                            setNewKey({ name: '', provider: '', key: '' });
                                        }}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        onClick={editingKey ? handleUpdateKey : handleAddKey}
                                        disabled={!newKey.name || !newKey.provider || !newKey.key}
                                    >
                                        {editingKey ? 'Update' : 'Add'} Key
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            </CardHeader>
            <CardContent>
                <Tabs defaultValue="all" className="w-full">
                    <TabsList>
                        <TabsTrigger value="all">All Keys</TabsTrigger>
                        <TabsTrigger value="active">Active</TabsTrigger>
                        <TabsTrigger value="providers">By Provider</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="all" className="space-y-3">
                        {apiKeys.length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground">
                                <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No API keys configured</p>
                                <p className="text-sm">Add an API key to get started</p>
                            </div>
                        ) : (
                            apiKeys.map(key => (
                                <div key={key.id} className="border rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-2">
                                                <h3 className="font-semibold">{key.name}</h3>
                                                <Badge variant="outline">
                                                    {getProviderName(key.provider)}
                                                </Badge>
                                                {key.isActive && (
                                                    <Badge variant="default">
                                                        <Check className="h-3 w-3 mr-1" />
                                                        Active
                                                    </Badge>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                <span>
                                                    {showKey[key.id] ? key.key : maskKey(key.key)}
                                                </span>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => toggleShowKey(key.id)}
                                                >
                                                    {showKey[key.id] ? (
                                                        <EyeOff className="h-3 w-3" />
                                                    ) : (
                                                        <Eye className="h-3 w-3" />
                                                    )}
                                                </Button>
                                            </div>
                                        </div>
                                        
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleToggleActive(key.id)}
                                            >
                                                {key.isActive ? (
                                                    <X className="h-4 w-4" />
                                                ) : (
                                                    <Check className="h-4 w-4" />
                                                )}
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleEditKey(key)}
                                            >
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDeleteKey(key.id)}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </TabsContent>
                    
                    <TabsContent value="active" className="space-y-3">
                        {apiKeys.filter(key => key.isActive).map(key => (
                            <div key={key.id} className="border rounded-lg p-4 bg-green-50 dark:bg-green-950">
                                <div className="flex items-center gap-2">
                                    <Shield className="h-4 w-4 text-green-600" />
                                    <span className="font-semibold">{key.name}</span>
                                    <Badge variant="outline">{getProviderName(key.provider)}</Badge>
                                </div>
                            </div>
                        ))}
                    </TabsContent>
                    
                    <TabsContent value="providers" className="space-y-4">
                        {Object.entries(getKeysByProvider()).map(([provider, keys]) => (
                            <div key={provider} className="border rounded-lg p-4">
                                <h3 className="font-semibold mb-2">{getProviderName(provider)}</h3>
                                <div className="space-y-2">
                                    {keys.map(key => (
                                        <div key={key.id} className="flex items-center justify-between text-sm">
                                            <span>{key.name}</span>
                                            {key.isActive && (
                                                <Badge variant="default" className="text-xs">Active</Badge>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
};
