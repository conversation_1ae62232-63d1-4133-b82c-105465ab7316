# Install dependencies and customize sandbox
FROM node:21-slim

R<PERSON> apt-get update && apt-get install -y     curl     git     python3     python3-pip     python3-venv     libpq5     && apt-get clean && rm -rf /var/lib/apt/lists/*


# The installer requires curl (and certificates) to download the release archive
RUN apt-get update && apt-get install -y --no-install-recommends curl ca-certificates

# Download the latest installer
ADD https://astral.sh/uv/install.sh /uv-installer.sh

# Run the installer then remove it
RUN sh /uv-installer.sh && rm /uv-installer.sh

# Install pnpm
RUN npm install -g pnpm

# Move uv to non-root path
RUN cp /root/.local/bin/uv /usr/local/bin/uv

# Ensure the installed binary is on the `PATH`
ENV PATH="/root/.local/bin/:$PATH"

# Install dependencies and customize sandbox
WORKDIR /home/<USER>

# Set up Git configurations
RUN git config --global user.email "<EMAIL>" &&     git config --global user.name "lumenary-infra"

# Add build argument for cache busting
ARG BUST=1

# Create app directory and copy template files
RUN mkdir -p /home/<USER>/app
COPY app/ /home/<USER>/app/
WORKDIR /home/<USER>/app
RUN mkdir -p /home/<USER>/app/logs
RUN pnpm install

# Create a directory for the FastAPI server
RUN mkdir -p /home/<USER>/services
COPY services/ /home/<USER>/services/

# Create a directory for the FastAPI logging server
RUN mkdir -p /home/<USER>/logging-server
COPY logging-server/ /home/<USER>/logging-server/

# Sync the project into a new environment, using the frozen lockfile
WORKDIR /home/<USER>/services
RUN uv add pyre-check
RUN uv run playwright install
RUN uv run playwright install-deps
RUN uv sync --frozen