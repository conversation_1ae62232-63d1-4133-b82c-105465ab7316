import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useGoogleAuthContext } from './GoogleAuthProvider';
import { Chrome } from 'lucide-react';

export const GoogleLoginPage: React.FC = () => {
    const { login, authLoading, appName } = useGoogleAuthContext();

    const handleGoogleLogin = async () => {
        try {
            await login();
        } catch (error) {
            console.error('Login failed:', error);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <CardTitle className="text-2xl font-bold">
                        Welcome to {appName || 'Veo 3 Prompt Architect'}
                    </CardTitle>
                    <CardDescription>
                        Sign in with your Google account to continue
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Button
                        onClick={handleGoogleLogin}
                        disabled={authLoading}
                        className="w-full"
                        size="lg"
                    >
                        <Chrome className="mr-2 h-5 w-5" />
                        {authLoading ? 'Signing in...' : 'Sign in with Google'}
                    </Button>
                    
                    <div className="text-center text-sm text-muted-foreground">
                        <p>
                            By signing in, you agree to our terms of service and privacy policy.
                        </p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};
