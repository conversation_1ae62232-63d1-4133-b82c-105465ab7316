import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    ArrowRight, 
    Wand2, 
    Copy, 
    Check,
    Film,
    Users,
    MessageSquare,
    MapPin,
    Clock
} from 'lucide-react';
import { ScreenplayParser, type ParsedScene } from '@/utils/screenplayParser';

interface SceneConverterProps {
    scene: ParsedScene;
    onConvert?: (promptData: any) => void;
}

interface ConversionSettings {
    includeDialogue: boolean;
    includeAction: boolean;
    includeCharacters: boolean;
    visualStyle: string;
    cameraAngle: string;
    lighting: string;
    mood: string;
}

const VISUAL_STYLES = [
    'Cinematic, 35mm film',
    'Hyperrealistic, 8K',
    'Film noir, black and white',
    'Vintage 1980s aesthetic',
    'Modern digital cinema',
    'Documentary style',
    'Artistic, painterly'
];

const CAMERA_ANGLES = [
    'Eye-Level',
    'High-Angle',
    'Low-Angle',
    'Dutch Angle',
    "Bird's-Eye View",
    'Worm's-Eye View'
];

const LIGHTING_OPTIONS = [
    'Natural lighting',
    'Dramatic, high-contrast',
    'Soft, diffused lighting',
    'Golden hour',
    'Blue hour',
    'Neon lighting',
    'Candlelight',
    'Harsh fluorescent'
];

const MOOD_OPTIONS = [
    'Neutral',
    'Tense',
    'Romantic',
    'Mysterious',
    'Action-packed',
    'Melancholic',
    'Uplifting',
    'Dark and moody'
];

export const SceneConverter: React.FC<SceneConverterProps> = ({
    scene,
    onConvert
}) => {
    const [settings, setSettings] = useState<ConversionSettings>({
        includeDialogue: true,
        includeAction: true,
        includeCharacters: true,
        visualStyle: 'Cinematic, 35mm film',
        cameraAngle: 'Eye-Level',
        lighting: 'Natural lighting',
        mood: 'Neutral'
    });

    const [convertedData, setConvertedData] = useState<any>(null);
    const [isCopied, setIsCopied] = useState(false);

    const handleConvert = () => {
        const baseData = ScreenplayParser.convertSceneToPromptData(scene);
        
        const promptData = {
            ...baseData,
            // Apply conversion settings
            dialogue_entries: settings.includeDialogue ? baseData.dialogue_entries : [],
            action: settings.includeAction ? baseData.action : '',
            scene_subjects: settings.includeCharacters ? baseData.scene_subjects : [],
            visual_aesthetic: settings.visualStyle,
            angle: settings.cameraAngle,
            lighting_design: settings.lighting,
            // Generate enhanced environment description
            environment: generateEnhancedEnvironment(scene, settings),
            // Generate sound design based on scene content
            sound_design: generateSoundDesign(scene, settings)
        };

        setConvertedData(promptData);
        onConvert?.(promptData);
    };

    const generateEnhancedEnvironment = (scene: ParsedScene, settings: ConversionSettings): string => {
        let environment = `${scene.location} - ${scene.timeOfDay}`;
        
        // Add mood-based environmental details
        switch (settings.mood) {
            case 'Tense':
                environment += '. The atmosphere is thick with tension and unease.';
                break;
            case 'Romantic':
                environment += '. The setting has a warm, intimate atmosphere.';
                break;
            case 'Mysterious':
                environment += '. Shadows and mystery permeate the environment.';
                break;
            case 'Dark and moody':
                environment += '. The environment is dark, brooding, and atmospheric.';
                break;
            default:
                if (scene.action.length > 0) {
                    environment += `. ${scene.action[0]}`;
                }
        }

        return environment;
    };

    const generateSoundDesign = (scene: ParsedScene, settings: ConversionSettings): string => {
        const soundElements = [];

        // Base environmental sounds
        if (scene.location.toLowerCase().includes('street') || scene.location.toLowerCase().includes('city')) {
            soundElements.push('urban ambience, distant traffic');
        } else if (scene.location.toLowerCase().includes('forest') || scene.location.toLowerCase().includes('outdoor')) {
            soundElements.push('natural ambience, birds chirping');
        } else if (scene.location.toLowerCase().includes('office') || scene.location.toLowerCase().includes('building')) {
            soundElements.push('indoor ambience, subtle background noise');
        }

        // Time-based sounds
        if (scene.timeOfDay.toLowerCase().includes('night')) {
            soundElements.push('nighttime atmosphere');
        } else if (scene.timeOfDay.toLowerCase().includes('morning')) {
            soundElements.push('morning ambience');
        }

        // Mood-based audio
        switch (settings.mood) {
            case 'Tense':
                soundElements.push('subtle tension-building music');
                break;
            case 'Romantic':
                soundElements.push('soft, romantic background music');
                break;
            case 'Action-packed':
                soundElements.push('dynamic, energetic soundtrack');
                break;
            case 'Mysterious':
                soundElements.push('mysterious, atmospheric music');
                break;
        }

        return soundElements.join(', ');
    };

    const copyToClipboard = async () => {
        if (!convertedData) return;

        const text = JSON.stringify(convertedData, null, 2);
        try {
            await navigator.clipboard.writeText(text);
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
        }
    };

    const updateSetting = <K extends keyof ConversionSettings>(
        key: K, 
        value: ConversionSettings[K]
    ) => {
        setSettings(prev => ({ ...prev, [key]: value }));
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Wand2 className="h-5 w-5" />
                    Scene to Prompt Converter
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Scene Overview */}
                <div className="p-4 bg-muted rounded-lg">
                    <h3 className="font-semibold mb-2">{scene.heading}</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            <span>{scene.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            <span>{scene.timeOfDay}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            <span>{scene.characters.length} characters</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <MessageSquare className="h-4 w-4" />
                            <span>{scene.dialogue.length} dialogue lines</span>
                        </div>
                    </div>
                </div>

                {/* Conversion Settings */}
                <div className="space-y-4">
                    <h4 className="font-semibold">Conversion Settings</h4>
                    
                    {/* Include Options */}
                    <div className="grid grid-cols-3 gap-4">
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                checked={settings.includeDialogue}
                                onChange={(e) => updateSetting('includeDialogue', e.target.checked)}
                                className="rounded"
                            />
                            <span className="text-sm">Include Dialogue</span>
                        </label>
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                checked={settings.includeAction}
                                onChange={(e) => updateSetting('includeAction', e.target.checked)}
                                className="rounded"
                            />
                            <span className="text-sm">Include Action</span>
                        </label>
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                checked={settings.includeCharacters}
                                onChange={(e) => updateSetting('includeCharacters', e.target.checked)}
                                className="rounded"
                            />
                            <span className="text-sm">Include Characters</span>
                        </label>
                    </div>

                    {/* Style Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label>Visual Style</Label>
                            <Select
                                value={settings.visualStyle}
                                onValueChange={(value) => updateSetting('visualStyle', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {VISUAL_STYLES.map(style => (
                                        <SelectItem key={style} value={style}>
                                            {style}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>Camera Angle</Label>
                            <Select
                                value={settings.cameraAngle}
                                onValueChange={(value) => updateSetting('cameraAngle', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {CAMERA_ANGLES.map(angle => (
                                        <SelectItem key={angle} value={angle}>
                                            {angle}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>Lighting</Label>
                            <Select
                                value={settings.lighting}
                                onValueChange={(value) => updateSetting('lighting', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {LIGHTING_OPTIONS.map(lighting => (
                                        <SelectItem key={lighting} value={lighting}>
                                            {lighting}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label>Mood</Label>
                            <Select
                                value={settings.mood}
                                onValueChange={(value) => updateSetting('mood', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {MOOD_OPTIONS.map(mood => (
                                        <SelectItem key={mood} value={mood}>
                                            {mood}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </div>

                {/* Convert Button */}
                <Button onClick={handleConvert} className="w-full" size="lg">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Convert Scene to Prompt Data
                </Button>

                {/* Converted Output */}
                {convertedData && (
                    <div className="space-y-4">
                        <Separator />
                        <div className="flex items-center justify-between">
                            <h4 className="font-semibold">Converted Prompt Data</h4>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={copyToClipboard}
                            >
                                {isCopied ? (
                                    <Check className="h-4 w-4 mr-2" />
                                ) : (
                                    <Copy className="h-4 w-4 mr-2" />
                                )}
                                {isCopied ? 'Copied!' : 'Copy'}
                            </Button>
                        </div>
                        
                        <div className="p-4 bg-muted rounded-lg">
                            <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                                {JSON.stringify(convertedData, null, 2)}
                            </pre>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
