// Define theme interface
export interface Theme {
  name: string;
  label: string;
  cssVars: {
    light: Record<string, string>;
    dark: Record<string, string>;
  };
}

// Default theme (current theme)
export const defaultTheme: Theme = {
  name: "default",
  label: "Default",
  cssVars: {
    light: {
      "--background": "0 0% 100%",
      "--foreground": "240 10% 3.9%",
      "--card": "0 0% 100%",
      "--card-foreground": "240 10% 3.9%",
      "--popover": "0 0% 100%",
      "--popover-foreground": "240 10% 3.9%",
      "--primary": "240 5.9% 10%",
      "--primary-foreground": "0 0% 98%",
      "--secondary": "240 4.8% 95.9%",
      "--secondary-foreground": "240 5.9% 10%",
      "--muted": "240 4.8% 95.9%",
      "--muted-foreground": "240 3.8% 46.1%",
      "--accent": "240 4.8% 95.9%",
      "--accent-foreground": "240 5.9% 10%",
      "--destructive": "0 84.2% 60.2%",
      "--destructive-foreground": "0 0% 98%",
      "--border": "240 5.9% 90%",
      "--input": "240 5.9% 90%",
      "--ring": "240 10% 3.9%",
      "--radius": "0.5rem",
      "--chart-1": "12 76% 61%",
      "--chart-2": "173 58% 39%",
      "--chart-3": "197 37% 24%",
      "--chart-4": "43 74% 66%",
      "--chart-5": "27 87% 67%",
    },
    dark: {
      "--background": "240 10% 3.9%",
      "--foreground": "0 0% 98%",
      "--card": "240 10% 3.9%",
      "--card-foreground": "0 0% 98%",
      "--popover": "240 10% 3.9%",
      "--popover-foreground": "0 0% 98%",
      "--primary": "0 0% 98%",
      "--primary-foreground": "240 5.9% 10%",
      "--secondary": "240 3.7% 15.9%",
      "--secondary-foreground": "0 0% 98%",
      "--muted": "240 3.7% 15.9%",
      "--muted-foreground": "240 5% 64.9%",
      "--accent": "240 3.7% 15.9%",
      "--accent-foreground": "0 0% 98%",
      "--destructive": "0 62.8% 30.6%",
      "--destructive-foreground": "0 0% 98%",
      "--border": "0 0% 17%",
      "--input": "0 0% 17%",
      "--ring": "0 0% 17%",
      "--chart-1": "12 76% 61%",
      "--chart-2": "173 58% 49%",
      "--chart-3": "197 47% 44%",
      "--chart-4": "43 84% 76%",
      "--chart-5": "27 97% 77%",
    },
  },
};

// Purple theme
export const purpleTheme: Theme = {
  name: "purple",
  label: "Purple",
  cssVars: {
    light: {
      "--background": "260 35% 98%",
      "--foreground": "224 71.4% 4.1%",
      "--card": "260 40% 99%",
      "--card-foreground": "224 71.4% 4.1%",
      "--popover": "260 40% 99%",
      "--popover-foreground": "224 71.4% 4.1%",
      "--primary": "262.1 83.3% 57.8%",
      "--primary-foreground": "210 20% 98%",
      "--secondary": "263 14.3% 95.9%",
      "--secondary-foreground": "220.9 39.3% 11%",
      "--muted": "263 14.3% 95.9%",
      "--muted-foreground": "220 8.9% 46.1%",
      "--accent": "263 14.3% 94%",
      "--accent-foreground": "220.9 39.3% 11%",
      "--destructive": "0 84.2% 60.2%",
      "--destructive-foreground": "210 20% 98%",
      "--border": "220 13% 91%",
      "--input": "220 13% 91%",
      "--ring": "262.1 83.3% 57.8%",
      "--radius": "1rem",
      "--chart-1": "12 76% 61%",
      "--chart-2": "173 58% 39%",
      "--chart-3": "197 37% 24%",
      "--chart-4": "43 74% 66%",
      "--chart-5": "27 87% 67%",
    },
    dark: {
      "--background": "260 50% 5%",
      "--foreground": "210 20% 98%",
      "--card": "260 50% 7%",
      "--card-foreground": "210 20% 98%",
      "--popover": "260 50% 7%",
      "--popover-foreground": "210 20% 98%",
      "--primary": "263.4 70% 50.4%",
      "--primary-foreground": "210 20% 98%",
      "--secondary": "263 30% 15%",
      "--secondary-foreground": "210 20% 98%",
      "--muted": "263 30% 15%",
      "--muted-foreground": "217.9 10.6% 64.9%",
      "--accent": "263 30% 15%",
      "--accent-foreground": "210 20% 98%",
      "--destructive": "0 62.8% 30.6%",
      "--destructive-foreground": "210 20% 98%",
      "--border": "0 0% 17%",
      "--input": "0 0% 17%",
      "--ring": "0 0% 17%",
      "--chart-1": "220 70% 50%",
      "--chart-2": "160 60% 45%",
      "--chart-3": "30 80% 55%",
      "--chart-4": "280 65% 60%",
      "--chart-5": "340 75% 55%",
    },
  },
};

// Blue theme
export const blueTheme: Theme = {
  name: "blue",
  label: "Blue",
  cssVars: {
    light: {
      "--background": "210 40% 98%",
      "--foreground": "222.2 84% 4.9%",
      "--card": "210 45% 99%",
      "--card-foreground": "222.2 84% 4.9%",
      "--popover": "210 45% 99%",
      "--popover-foreground": "222.2 84% 4.9%",
      "--primary": "221.2 83.2% 53.3%",
      "--primary-foreground": "210 40% 98%",
      "--secondary": "210 40% 96.1%",
      "--secondary-foreground": "222.2 47.4% 11.2%",
      "--muted": "210 40% 96.1%",
      "--muted-foreground": "215.4 16.3% 46.9%",
      "--accent": "210 40% 96.1%",
      "--accent-foreground": "222.2 47.4% 11.2%",
      "--destructive": "0 84.2% 60.2%",
      "--destructive-foreground": "210 40% 98%",
      "--border": "214.3 31.8% 91.4%",
      "--input": "214.3 31.8% 91.4%",
      "--ring": "221.2 83.2% 53.3%",
      "--radius": "0.75rem",
      "--chart-1": "221.2 83.2% 53.3%",
      "--chart-2": "180 70% 45%",
      "--chart-3": "245 58% 50%",
      "--chart-4": "200 95% 60%",
      "--chart-5": "190 80% 40%",
    },
    dark: {
      "--background": "220 65% 5%",
      "--foreground": "210 40% 98%",
      "--card": "220 65% 7%",
      "--card-foreground": "210 40% 98%",
      "--popover": "220 65% 7%",
      "--popover-foreground": "210 40% 98%",
      "--primary": "217.2 91.2% 59.8%",
      "--primary-foreground": "222.2 47.4% 11.2%",
      "--secondary": "217.2 32.6% 17.5%",
      "--secondary-foreground": "210 40% 98%",
      "--muted": "217.2 32.6% 17.5%",
      "--muted-foreground": "215 20.2% 65.1%",
      "--accent": "217.2 32.6% 17.5%",
      "--accent-foreground": "210 40% 98%",
      "--destructive": "0 62.8% 30.6%",
      "--destructive-foreground": "210 40% 98%",
      "--border": "0 0% 17%",
      "--input": "0 0% 17%",
      "--ring": "0 0% 17%",
      "--chart-1": "217.2 91.2% 59.8%",
      "--chart-2": "180 80% 55%",
      "--chart-3": "245 68% 60%",
      "--chart-4": "200 90% 70%",
      "--chart-5": "190 85% 50%",
    },
  },
};

// Green theme
export const greenTheme: Theme = {
  name: "green",
  label: "Green",
  cssVars: {
    light: {
      "--background": "0 0% 100%",
      "--foreground": "240 10% 3.9%",
      "--card": "0 0% 100%",
      "--card-foreground": "240 10% 3.9%",
      "--popover": "0 0% 100%",
      "--popover-foreground": "240 10% 3.9%",
      "--primary": "142.1 76.2% 36.3%",
      "--primary-foreground": "355.7 100% 97.3%",
      "--secondary": "240 4.8% 95.9%",
      "--secondary-foreground": "240 5.9% 10%",
      "--muted": "240 4.8% 95.9%",
      "--muted-foreground": "240 3.8% 46.1%",
      "--accent": "240 4.8% 95.9%",
      "--accent-foreground": "240 5.9% 10%",
      "--destructive": "0 84.2% 60.2%",
      "--destructive-foreground": "0 0% 98%",
      "--border": "240 5.9% 90%",
      "--input": "240 5.9% 90%",
      "--ring": "142.1 76.2% 36.3%",
      "--radius": "0.5rem",
      "--chart-1": "142.1 76.2% 46.3%",
      "--chart-2": "180 70% 45%",
      "--chart-3": "120 60% 50%",
      "--chart-4": "160 95% 35%",
      "--chart-5": "90 80% 40%",
    },
    dark: {
      "--background": "20 14.3% 4.1%",
      "--foreground": "0 0% 95%",
      "--card": "24 9.8% 10%",
      "--card-foreground": "0 0% 95%",
      "--popover": "0 0% 9%",
      "--popover-foreground": "0 0% 95%",
      "--primary": "142.1 70.6% 45.3%",
      "--primary-foreground": "144.9 80.4% 10%",
      "--secondary": "240 3.7% 15.9%",
      "--secondary-foreground": "0 0% 98%",
      "--muted": "0 0% 15%",
      "--muted-foreground": "240 5% 64.9%",
      "--accent": "12 6.5% 15.1%",
      "--accent-foreground": "0 0% 98%",
      "--destructive": "0 62.8% 30.6%",
      "--destructive-foreground": "0 85.7% 97.3%",
      "--border": "240 3.7% 15.9%",
      "--input": "240 3.7% 15.9%",
      "--ring": "240 3.7% 15.9%",
      "--chart-1": "142.1 70.6% 50.3%",
      "--chart-2": "180 80% 55%",
      "--chart-3": "120 70% 60%",
      "--chart-4": "160 90% 45%",
      "--chart-5": "90 85% 50%",
    },
  },
};

// Red theme
export const redTheme: Theme = {
  name: "red",
  label: "Red",
  cssVars: {
    light: {
      "--background": "0 0% 100%",
      "--foreground": "0 0% 3.9%",
      "--card": "0 0% 100%",
      "--card-foreground": "0 0% 3.9%",
      "--popover": "0 0% 100%",
      "--popover-foreground": "0 0% 3.9%",
      "--primary": "0 72.2% 50.6%",
      "--primary-foreground": "0 85.7% 97.3%",
      "--secondary": "0 0% 96.1%",
      "--secondary-foreground": "0 0% 9%",
      "--muted": "0 0% 96.1%",
      "--muted-foreground": "0 0% 45.1%",
      "--accent": "0 0% 96.1%",
      "--accent-foreground": "0 0% 9%",
      "--destructive": "0 84.2% 60.2%",
      "--destructive-foreground": "0 0% 98%",
      "--border": "0 0% 89.8%",
      "--input": "0 0% 89.8%",
      "--ring": "0 72.2% 50.6%",
      "--radius": "0.75rem",
      "--chart-1": "0 72.2% 55.6%",
      "--chart-2": "30 90% 60%",
      "--chart-3": "350 80% 50%",
      "--chart-4": "15 95% 65%",
      "--chart-5": "380 75% 55%",
    },
    dark: {
      "--background": "0 0% 3.9%",
      "--foreground": "0 0% 98%",
      "--card": "0 0% 3.9%",
      "--card-foreground": "0 0% 98%",
      "--popover": "0 0% 3.9%",
      "--popover-foreground": "0 0% 98%",
      "--primary": "0 72.2% 50.6%",
      "--primary-foreground": "0 85.7% 97.3%",
      "--secondary": "0 0% 14.9%",
      "--secondary-foreground": "0 0% 98%",
      "--muted": "0 0% 14.9%",
      "--muted-foreground": "0 0% 63.9%",
      "--accent": "0 0% 14.9%",
      "--accent-foreground": "0 0% 98%",
      "--destructive": "0 62.8% 30.6%",
      "--destructive-foreground": "0 0% 98%",
      "--border": "0 0% 17%",
      "--input": "0 0% 17%",
      "--ring": "0 0% 17%",
      "--chart-1": "0 72.2% 60.6%",
      "--chart-2": "30 95% 65%",
      "--chart-3": "350 85% 55%",
      "--chart-4": "15 90% 70%",
      "--chart-5": "380 80% 60%",
    },
  },
};

// Collection of all available themes
export const allThemes: Theme[] = [
  defaultTheme,
  purpleTheme,
  blueTheme,
  greenTheme,
  redTheme,
];

// Function to get a theme by name
export function getTheme(name: string): Theme {
  return allThemes.find((theme) => theme.name === name) || defaultTheme;
}