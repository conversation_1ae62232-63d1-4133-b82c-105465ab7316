from solar import Table, ColumnDetails
from typing import Optional
from datetime import datetime
import uuid

class Subject(Table):
    __tablename__ = "subjects"
    
    id: uuid.UUID = ColumnDetails(default_factory=uuid.uuid4, primary_key=True)
    project_id: uuid.UUID  # Reference to project
    
    name: str  # e.g., "Detective <PERSON>", "The Dragon"
    description: str  # e.g., "A grizzled detective in a trench coat"
    character_reference_url: Optional[str] = None  # Uploaded image URL
    
    # Subject consistency across scenes
    is_primary_character: bool = ColumnDetails(default_factory=lambda: False)
    
    created_at: datetime = ColumnDetails(default_factory=datetime.now)
    last_updated: datetime = ColumnDetails(default_factory=datetime.now)