import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Play, Pause, SkipBack, SkipForward, Volume2 } from 'lucide-react';

interface AudioWaveformProps {
    audioSrc?: string;
    audioBlob?: Blob;
    title?: string;
    onTimeUpdate?: (currentTime: number, duration: number) => void;
}

export const AudioWaveform: React.FC<AudioWaveformProps> = ({
    audioSrc,
    audioBlob,
    title = "Audio Waveform",
    onTimeUpdate
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const audioRef = useRef<HTMLAudioElement>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const analyserRef = useRef<AnalyserNode | null>(null);
    const sourceRef = useRef<MediaElementAudioSourceNode | null>(null);
    const animationRef = useRef<number | null>(null);

    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(0.8);
    const [audioUrl, setAudioUrl] = useState<string>('');

    useEffect(() => {
        if (audioBlob) {
            const url = URL.createObjectURL(audioBlob);
            setAudioUrl(url);
            return () => URL.revokeObjectURL(url);
        } else if (audioSrc) {
            setAudioUrl(audioSrc);
        }
    }, [audioBlob, audioSrc]);

    useEffect(() => {
        const audio = audioRef.current;
        if (!audio || !audioUrl) return;

        audio.src = audioUrl;

        const handleLoadedMetadata = () => {
            setDuration(audio.duration);
            setupAudioContext();
        };

        const handleTimeUpdate = () => {
            setCurrentTime(audio.currentTime);
            onTimeUpdate?.(audio.currentTime, audio.duration);
        };

        const handleEnded = () => {
            setIsPlaying(false);
            setCurrentTime(0);
        };

        audio.addEventListener('loadedmetadata', handleLoadedMetadata);
        audio.addEventListener('timeupdate', handleTimeUpdate);
        audio.addEventListener('ended', handleEnded);

        return () => {
            audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
            audio.removeEventListener('timeupdate', handleTimeUpdate);
            audio.removeEventListener('ended', handleEnded);
        };
    }, [audioUrl, onTimeUpdate]);

    const setupAudioContext = () => {
        const audio = audioRef.current;
        if (!audio) return;

        try {
            const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
            const analyser = audioContext.createAnalyser();
            const source = audioContext.createMediaElementSource(audio);

            analyser.fftSize = 256;
            source.connect(analyser);
            analyser.connect(audioContext.destination);

            audioContextRef.current = audioContext;
            analyserRef.current = analyser;
            sourceRef.current = source;

            drawWaveform();
        } catch (error) {
            console.error('Error setting up audio context:', error);
        }
    };

    const drawWaveform = () => {
        const canvas = canvasRef.current;
        const analyser = analyserRef.current;
        
        if (!canvas || !analyser) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const draw = () => {
            animationRef.current = requestAnimationFrame(draw);

            analyser.getByteFrequencyData(dataArray);

            ctx.fillStyle = 'rgb(15, 23, 42)'; // bg-slate-900
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const barWidth = (canvas.width / bufferLength) * 2.5;
            let barHeight;
            let x = 0;

            for (let i = 0; i < bufferLength; i++) {
                barHeight = (dataArray[i] / 255) * canvas.height;

                // Create gradient
                const gradient = ctx.createLinearGradient(0, canvas.height - barHeight, 0, canvas.height);
                gradient.addColorStop(0, 'rgb(59, 130, 246)'); // blue-500
                gradient.addColorStop(0.5, 'rgb(147, 51, 234)'); // purple-500
                gradient.addColorStop(1, 'rgb(236, 72, 153)'); // pink-500

                ctx.fillStyle = gradient;
                ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

                x += barWidth + 1;
            }
        };

        draw();
    };

    const togglePlayPause = () => {
        const audio = audioRef.current;
        if (!audio) return;

        if (isPlaying) {
            audio.pause();
        } else {
            audio.play();
        }
        setIsPlaying(!isPlaying);
    };

    const handleSeek = (value: number[]) => {
        const audio = audioRef.current;
        if (!audio || !duration) return;

        const newTime = (value[0] / 100) * duration;
        audio.currentTime = newTime;
        setCurrentTime(newTime);
    };

    const handleVolumeChange = (value: number[]) => {
        const newVolume = value[0] / 100;
        setVolume(newVolume);
        
        const audio = audioRef.current;
        if (audio) {
            audio.volume = newVolume;
        }
    };

    const skipBackward = () => {
        const audio = audioRef.current;
        if (!audio) return;
        
        audio.currentTime = Math.max(0, audio.currentTime - 10);
    };

    const skipForward = () => {
        const audio = audioRef.current;
        if (!audio) return;
        
        audio.currentTime = Math.min(duration, audio.currentTime + 10);
    };

    const formatTime = (time: number) => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    useEffect(() => {
        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
            if (audioContextRef.current) {
                audioContextRef.current.close();
            }
        };
    }, []);

    if (!audioUrl) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>{title}</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center h-32 text-muted-foreground">
                        No audio loaded
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* Waveform Canvas */}
                <div className="relative">
                    <canvas
                        ref={canvasRef}
                        width={800}
                        height={200}
                        className="w-full h-32 bg-slate-900 rounded-lg"
                    />
                    
                    {/* Progress overlay */}
                    <div 
                        className="absolute top-0 left-0 h-full bg-blue-500/20 rounded-lg transition-all duration-100"
                        style={{ 
                            width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%' 
                        }}
                    />
                </div>

                {/* Progress Slider */}
                <div className="space-y-2">
                    <Slider
                        value={[duration > 0 ? (currentTime / duration) * 100 : 0]}
                        onValueChange={handleSeek}
                        max={100}
                        step={0.1}
                        className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{formatTime(currentTime)}</span>
                        <span>{formatTime(duration)}</span>
                    </div>
                </div>

                {/* Controls */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={skipBackward}
                            disabled={!audioUrl}
                        >
                            <SkipBack className="h-4 w-4" />
                        </Button>
                        
                        <Button
                            onClick={togglePlayPause}
                            disabled={!audioUrl}
                            size="sm"
                        >
                            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                        
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={skipForward}
                            disabled={!audioUrl}
                        >
                            <SkipForward className="h-4 w-4" />
                        </Button>
                    </div>

                    {/* Volume Control */}
                    <div className="flex items-center gap-2 w-32">
                        <Volume2 className="h-4 w-4" />
                        <Slider
                            value={[volume * 100]}
                            onValueChange={handleVolumeChange}
                            max={100}
                            step={1}
                            className="flex-1"
                        />
                    </div>
                </div>

                {/* Hidden Audio Element */}
                <audio
                    ref={audioRef}
                    crossOrigin="anonymous"
                    style={{ display: 'none' }}
                />
            </CardContent>
        </Card>
    );
};
