# Auto-generated by Lumenary

##############################################################################
# Dependencies
##############################################################################

import json
from typing import Any, Dict, Optional

from fastapi.encoders import jsonable_encoder
from starlette.responses import HTMLResponse
from typing_extensions import Annotated, Doc

##############################################################################
# Dependencies
##############################################################################
swagger_ui_default_parameters: Annotated[
    Dict[str, Any],
    Doc(
        """
        Default configurations for Swagger UI.

        You can use it as a template to add any other configurations needed.
        """
    ),
] = {
    "dom_id": "#swagger-ui",
    "layout": "BaseLayout",
    "deepLinking": True,
    "showExtensions": True,
    "showCommonExtensions": True,
}


def get_swagger_ui_html(
    *,
    openapi_url: Annotated[
        str,
        Doc(
            """
            The OpenAPI URL that Swagger UI should load and use.

            This is normally done automatically by FastAPI using the default URL
            `/openapi.json`.
            """
        ),
    ],
    title: Annotated[
        str,
        Doc(
            """
            The HTML `<title>` content, normally shown in the browser tab.
            """
        ),
    ],
    swagger_js_url: Annotated[
        str,
        Doc(
            """
            The URL to use to load the Swagger UI JavaScript.

            It is normally set to a CDN URL.
            """
        ),
    ] = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
    swagger_css_url: Annotated[
        str,
        Doc(
            """
            The URL to use to load the Swagger UI CSS.

            It is normally set to a CDN URL.
            """
        ),
    ] = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    swagger_favicon_url: Annotated[
        str,
        Doc(
            """
            The URL of the favicon to use. It is normally shown in the browser tab.
            """
        ),
    ] = "https://fastapi.tiangolo.com/img/favicon.png",
    oauth2_redirect_url: Annotated[
        Optional[str],
        Doc(
            """
            The OAuth2 redirect URL, it is normally automatically handled by FastAPI.
            """
        ),
    ] = None,
    init_oauth: Annotated[
        Optional[Dict[str, Any]],
        Doc(
            """
            A dictionary with Swagger UI OAuth2 initialization configurations.
            """
        ),
    ] = None,
    swagger_ui_parameters: Annotated[
        Optional[Dict[str, Any]],
        Doc(
            """
            Configuration parameters for Swagger UI.

            It defaults to [swagger_ui_default_parameters][fastapi.openapi.docs.swagger_ui_default_parameters].
            """
        ),
    ] = None,
) -> HTMLResponse:
    """
    Generate and return the HTML  that loads Swagger UI for the interactive
    API docs (normally served at `/docs`).

    You would only call this function yourself if you needed to override some parts,
    for example the URLs to use to load Swagger UI's JavaScript and CSS.

    Read more about it in the
    [FastAPI docs for Configure Swagger UI](https://fastapi.tiangolo.com/how-to/configure-swagger-ui/)
    and the [FastAPI docs for Custom Docs UI Static Assets (Self-Hosting)](https://fastapi.tiangolo.com/how-to/custom-docs-ui-assets/).
    """
    current_swagger_ui_parameters = swagger_ui_default_parameters.copy()
    if swagger_ui_parameters:
        current_swagger_ui_parameters.update(swagger_ui_parameters)

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <link type="text/css" rel="stylesheet" href="{swagger_css_url}">
    <link rel="shortcut icon" href="{swagger_favicon_url}">
    <title>{title}</title>
    <style>
    /* Override Swagger UI auth wrapper with high specificity */
    html body #swagger-ui .auth-wrapper,
    #swagger-ui .auth-wrapper * {{
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      height: 0 !important;
      width: 0 !important;
      pointer-events: none !important;
      position: absolute !important;
      overflow: hidden !important;
    }}
    </style>
    </head>
    <body>
    <script>
    const clientId = "a3bc4490-cec6-44e3-9d79-3ba4aa42a3c5";
    const backendUrl = "https://api.solarapp.dev";
    const frontendUrl = "https://solarapp.dev";
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    let token = null;
    let email = null;
    async function fetchToken() {{
        const response = await fetch(backendUrl + "/innerApp/oauth2/token", {{
            method: "POST",
            headers: {{
                "Content-Type": "application/json"
            }},
            body: JSON.stringify({{
                code: code,
                grant_type: "authorization_code",
                client_id: clientId,
                code_verifier: localStorage.getItem("codeVerifier"),
                omit_refresh_token: true
            }})
        }})
        const data = await response.json();
        token = data.access_token;
        localStorage.removeItem("codeVerifier");
    }}
    async function introspectToken() {{
        // Parse the token to extract the jit field
        const tokenParts = token.split('.');
        if (tokenParts.length >= 2) {{
            try {{
                const payload = JSON.parse(atob(tokenParts[1]));
                const jti = payload.jti;
                
                const response = await fetch(backendUrl + "/innerApp/oauth2/introspect", {{
                    method: "POST",
                    headers: {{
                        "Content-Type": "application/json"
                    }},
                    body: JSON.stringify({{
                        token: jti,
                        token_type_hint: "access_token"
                    }})
                }})
                const data = await response.json();
                email = data.email;
                renderTokenBanner();
            }} catch (e) {{
                console.error("Error parsing token:", e);
            }}
        }}
    }}
    if (code && token == null) {{
        fetchToken().then(() => introspectToken());
    }}

    const createCodeChallenge = async () => {{
        const charSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        const codeVerifier = Array.from({{length: 32}}, () => charSet[Math.floor(Math.random() * charSet.length)]).join('');

        const encoder = new TextEncoder();
        const data = encoder.encode(codeVerifier);
        const hash = await window.crypto.subtle.digest("SHA-256", data);
        const codeChallenge = btoa(String.fromCharCode(...new Uint8Array(hash)))
            .replace(/\+/g, "-")
            .replace(/\//g, "_")
            .replace(/=+$/, "");
        return {{ codeVerifier, codeChallenge }};
    }}

    const authenticate = async () => {{
        const {{ codeVerifier, codeChallenge }} = await createCodeChallenge();
        localStorage.setItem("codeVerifier", codeVerifier);
        
        if (clientId == null || backendUrl == null || frontendUrl == null) {{
            return;
        }}

        const authUrl = new URL(frontendUrl + "/external-login");
        authUrl.searchParams.set("client_id", clientId)
        authUrl.searchParams.append("code_challenge", codeChallenge)
        authUrl.searchParams.append("code_challenge_method", "S256")
        authUrl.searchParams.set("redirect_uri", window.location.origin + "/docs")
        window.location.href = authUrl.toString();
    }}
    </script>
    <div id="token-banner"></div>
    <button id="auth-button" style="margin-top: 10px; padding: 6px 12px;
    background-color:rgb(46, 137, 202); color: white; border: none;
    border-radius: 4px; cursor: pointer;" onclick="authenticate()">Authenticate</button>
    
    <div id="swagger-ui">
    </div>
    <script src="{swagger_js_url}"></script>
    <!-- `SwaggerUIBundle` is now available on the page -->
    <script>
    const ui = SwaggerUIBundle({{
        url: '{openapi_url}',
        requestInterceptor: (req) => {{
            if (token) {{
                req.headers["Authorization"] = "Bearer " + token;
            }}
            return req;
        }},
    """

    for key, value in current_swagger_ui_parameters.items():
        html += f"{json.dumps(key)}: {json.dumps(jsonable_encoder(value))},\n"

    if oauth2_redirect_url:
        html += f"oauth2RedirectUrl: window.location.origin + '{oauth2_redirect_url}',"

    html += """
    presets: [
        SwaggerUIBundle.presets.apis,
        SwaggerUIBundle.SwaggerUIStandalonePreset
        ],
    })"""

    if init_oauth:
        html += f"""
        ui.initOAuth({json.dumps(jsonable_encoder(init_oauth))})
        """

    html += """
    </script>
    <script>
    function renderTokenBanner() {{
        const tokenBanner = document.getElementById('token-banner');
        tokenBanner.style.padding = '10px';
        tokenBanner.style.backgroundColor = '#e8f4f8';
        tokenBanner.style.border = '1px solid #bce8f1';
        tokenBanner.style.borderRadius = '4px';
        tokenBanner.style.margin = '10px 0';
        tokenBanner.style.wordBreak = 'break-all';
        tokenBanner.style.fontFamily = '"Roboto","Helvetica Neue",Helvetica,Arial,sans-serif';
        tokenBanner.style.fontSize = '14px';
        tokenBanner.style.color = '#3b4151';
        const authButton = document.getElementById('auth-button');
        if (email) {{
            tokenBanner.innerHTML = `
                <strong>You are currently logged in as:</strong><br>
                <code>${email}</code>
            `;
            // Hide the authenticate button when user is logged in
            if (authButton) {{
                authButton.style.display = 'none';
            }}
        }} else {{
            tokenBanner.innerHTML = `
                <strong>You are not logged in.</strong>
            `;
            // Show the authenticate button when user is not logged in
            if (authButton) {{
                authButton.style.display = 'block';
            }}
        }}
    }}
    </script>
    </body>
    </html>
    """
    return HTMLResponse(html)


