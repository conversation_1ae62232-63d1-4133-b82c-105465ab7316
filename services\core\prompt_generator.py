from typing import Optional, List, Dict
from uuid import UUID
from datetime import datetime
from core.scene import Scene
from core.subject import Subject
from core.project import Project
from solar.access import User, authenticated

@authenticated
def create_scene(
    user: User,
    project_id: UUID,
    scene_number: int,
    scene_title: Optional[str] = None,
    action: Optional[str] = None,
    environment: Optional[str] = None,
    scene_subjects: Optional[List[str]] = None,
    dialogue_entries: Optional[List[Dict]] = None,
    composition: Optional[str] = None,
    angle: Optional[str] = None,
    camera_movements: Optional[List[str]] = None,
    visual_aesthetic: Optional[str] = None,
    lighting_design: Optional[str] = None,
    sound_design: Optional[str] = None,
    negative_prompt: Optional[str] = None,
    seed: Optional[int] = None,
    screenplay_content: Optional[str] = None
) -> Scene:
    """Create a new scene in a project."""
    
    # Verify the user owns the project
    project_results = Project.sql(
        "SELECT * FROM projects WHERE id = %(project_id)s AND user_id = %(user_id)s",
        {"project_id": project_id, "user_id": user.id}
    )
    
    if not project_results:
        raise ValueError("Project not found or not owned by user")
    
    project = Project(**project_results[0])
    
    # Generate the prompt from the provided inputs
    generated_prompt = generate_veo_prompt_v2(
        project=project,
        scene_subjects=scene_subjects or [],
        action=action,
        environment=environment,
        dialogue_entries=dialogue_entries or [],
        composition=composition,
        angle=angle,
        camera_movements=camera_movements or [],
        visual_aesthetic=visual_aesthetic,
        lighting_design=lighting_design,
        sound_design=sound_design,
        negative_prompt=negative_prompt
    )
    
    scene = Scene(
        project_id=project_id,
        scene_number=scene_number,
        scene_title=scene_title,
        action=action,
        environment=environment,
        scene_subjects=scene_subjects or [],
        dialogue_entries=dialogue_entries or [],
        composition=composition,
        angle=angle,
        camera_movements=camera_movements or [],
        visual_aesthetic=visual_aesthetic,
        lighting_design=lighting_design,
        sound_design=sound_design,
        negative_prompt=negative_prompt,
        seed=seed,
        screenplay_content=screenplay_content,
        generated_prompt=generated_prompt,
        last_updated=datetime.now()
    )
    
    scene.sync()
    return scene

@authenticated
def get_scene(user: User, scene_id: UUID) -> Optional[Scene]:
    """Get a scene by its ID."""
    results = Scene.sql(
        "SELECT s.* FROM scenes s JOIN projects p ON s.project_id = p.id WHERE s.id = %(scene_id)s AND p.user_id = %(user_id)s",
        {"scene_id": scene_id, "user_id": user.id}
    )
    if results:
        return Scene(**results[0])
    return None

@authenticated
def get_project_scenes(user: User, project_id: UUID) -> List[Scene]:
    """Get all scenes for a project."""
    # Verify user owns the project
    project_results = Project.sql(
        "SELECT * FROM projects WHERE id = %(project_id)s AND user_id = %(user_id)s",
        {"project_id": project_id, "user_id": user.id}
    )
    
    if not project_results:
        return []
    
    results = Scene.sql(
        "SELECT * FROM scenes WHERE project_id = %(project_id)s ORDER BY scene_number ASC",
        {"project_id": project_id}
    )
    return [Scene(**result) for result in results]

@authenticated
def update_scene(
    user: User,
    scene_id: UUID,
    scene_title: Optional[str] = None,
    action: Optional[str] = None,
    environment: Optional[str] = None,
    scene_subjects: Optional[List[str]] = None,
    dialogue_entries: Optional[List[Dict]] = None,
    composition: Optional[str] = None,
    angle: Optional[str] = None,
    camera_movements: Optional[List[str]] = None,
    visual_aesthetic: Optional[str] = None,
    lighting_design: Optional[str] = None,
    sound_design: Optional[str] = None,
    negative_prompt: Optional[str] = None,
    seed: Optional[int] = None,
    screenplay_content: Optional[str] = None
) -> Scene:
    """Update an existing scene and regenerate the prompt."""
    
    scene = get_scene(user, scene_id)
    if not scene:
        raise ValueError(f"Scene with ID {scene_id} not found or not owned by user")
        
    # Get the project for prompt generation
    project_results = Project.sql(
        "SELECT * FROM projects WHERE id = %(project_id)s",
        {"project_id": scene.project_id}
    )
    project = Project(**project_results[0]) if project_results else None
    
    # Update fields if provided
    if scene_title is not None:
        scene.scene_title = scene_title
    if action is not None:
        scene.action = action
    if environment is not None:
        scene.environment = environment
    if scene_subjects is not None:
        scene.scene_subjects = scene_subjects
    if dialogue_entries is not None:
        scene.dialogue_entries = dialogue_entries
    if composition is not None:
        scene.composition = composition
    if angle is not None:
        scene.angle = angle
    if camera_movements is not None:
        scene.camera_movements = camera_movements
    if visual_aesthetic is not None:
        scene.visual_aesthetic = visual_aesthetic
    if lighting_design is not None:
        scene.lighting_design = lighting_design
    if sound_design is not None:
        scene.sound_design = sound_design
    if negative_prompt is not None:
        scene.negative_prompt = negative_prompt
    if seed is not None:
        scene.seed = seed
    if screenplay_content is not None:
        scene.screenplay_content = screenplay_content
    
    # Regenerate the prompt
    if project:
        scene.generated_prompt = generate_veo_prompt_v2(
            project=project,
            scene_subjects=scene.scene_subjects,
            action=scene.action,
            environment=scene.environment,
            dialogue_entries=scene.dialogue_entries,
            composition=scene.composition,
            angle=scene.angle,
            camera_movements=scene.camera_movements,
            visual_aesthetic=scene.visual_aesthetic,
            lighting_design=scene.lighting_design,
            sound_design=scene.sound_design,
            negative_prompt=scene.negative_prompt
        )
    
    scene.last_updated = datetime.now()
    scene.sync()
    return scene

def generate_veo_prompt_v2(
    project: Project,
    scene_subjects: List[str] = None,
    action: Optional[str] = None,
    environment: Optional[str] = None,
    dialogue_entries: List[Dict] = None,
    composition: Optional[str] = None,
    angle: Optional[str] = None,
    camera_movements: List[str] = None,
    visual_aesthetic: Optional[str] = None,
    lighting_design: Optional[str] = None,
    sound_design: Optional[str] = None,
    negative_prompt: Optional[str] = None
) -> str:
    """Generate a structured Veo prompt from the provided components with character consistency."""
    
    camera_movements = camera_movements or []
    scene_subjects = scene_subjects or []
    dialogue_entries = dialogue_entries or []
    prompt_parts = []
    
    # Get subject details for character consistency
    subject_descriptions = []
    if scene_subjects:
        for subject_id in scene_subjects:
            try:
                subject_results = Subject.sql(
                    "SELECT * FROM subjects WHERE id = %(subject_id)s",
                    {"subject_id": UUID(subject_id)}
                )
                if subject_results:
                    subject = Subject(**subject_results[0])
                    subject_descriptions.append(subject.description)
            except:
                continue
    
    # Start with cinematography (composition and angle)
    cinematic_parts = []
    if composition:
        cinematic_parts.append(composition.lower())
    if angle:
        cinematic_parts.append(angle.lower())
    if camera_movements:
        movement_text = ", ".join([movement.lower() for movement in camera_movements])
        cinematic_parts.append(movement_text)
    
    if cinematic_parts:
        cinematic_intro = f"A {', '.join(cinematic_parts)}"
        prompt_parts.append(cinematic_intro)
    
    # Core scene elements
    scene_parts = []
    if subject_descriptions:
        subjects_text = ", ".join(subject_descriptions)
        scene_parts.append(subjects_text)
    
    if action and subject_descriptions:
        scene_parts.append(action)
    elif action:
        scene_parts.append(f"A scene showing {action}")
    
    if environment:
        if scene_parts:
            scene_parts.append(f"{environment}")
        else:
            scene_parts.append(environment)
    
    if scene_parts:
        scene_text = " ".join(scene_parts)
        if prompt_parts and any(cinematic_parts):
            # If we have cinematography, continue the sentence
            prompt_parts[0] = f"{prompt_parts[0]} of {scene_text}"
        else:
            # If no cinematography, start fresh
            prompt_parts.append(scene_text)
    
    # Visual aesthetic and lighting
    style_parts = []
    if visual_aesthetic:
        style_parts.append(f"The visual style is {visual_aesthetic}")
    if lighting_design:
        if visual_aesthetic:
            style_parts.append(f"with {lighting_design} lighting")
        else:
            style_parts.append(f"Lighting: {lighting_design}")
    
    if style_parts:
        prompt_parts.append(". ".join(style_parts))
    
    # Sound design
    if sound_design:
        prompt_parts.append(f"Audio: {sound_design}")
    
    # Combine all parts
    main_prompt = ". ".join([part for part in prompt_parts if part.strip()])
    if not main_prompt.endswith("."):
        main_prompt += "."
    
    # Clean up any double periods
    main_prompt = main_prompt.replace("..", ".")
    
    # Add negative prompt if provided
    if negative_prompt:
        main_prompt += f" Exclude: {negative_prompt}."
    
    return main_prompt