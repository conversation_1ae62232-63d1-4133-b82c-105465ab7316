export interface ScreenplayElement {
    type: 'scene_heading' | 'action' | 'character' | 'dialogue' | 'parenthetical' | 'transition' | 'shot';
    content: string;
    lineNumber: number;
}

export interface ParsedScene {
    sceneNumber: number;
    heading: string;
    location: string;
    timeOfDay: string;
    elements: ScreenplayElement[];
    characters: string[];
    dialogue: Array<{
        character: string;
        text: string;
        parenthetical?: string;
    }>;
    action: string[];
}

export interface ParsedScreenplay {
    title?: string;
    author?: string;
    scenes: ParsedScene[];
    characters: Set<string>;
}

export class ScreenplayParser {
    private static readonly SCENE_HEADING_REGEX = /^(INT\.|EXT\.|FADE IN:|FADE OUT:|CUT TO:)/i;
    private static readonly CHARACTER_REGEX = /^[A-Z][A-Z\s\d'.-]*$/;
    private static readonly PARENTHETICAL_REGEX = /^\([^)]*\)$/;
    private static readonly TRANSITION_REGEX = /^(FADE IN:|FADE OUT:|CUT TO:|DISSOLVE TO:|SMASH CUT TO:)/i;

    static parse(screenplayText: string): ParsedScreenplay {
        const lines = screenplayText.split('\n').map(line => line.trim());
        const elements: ScreenplayElement[] = [];
        const characters = new Set<string>();
        
        let title: string | undefined;
        let author: string | undefined;
        
        // Parse each line
        lines.forEach((line, index) => {
            if (!line) return;
            
            const element = this.parseLine(line, index + 1);
            if (element) {
                elements.push(element);
                
                if (element.type === 'character') {
                    characters.add(element.content);
                }
            }
            
            // Extract title and author from early lines
            if (index < 10) {
                if (line.toUpperCase().includes('TITLE:')) {
                    title = line.replace(/title:/i, '').trim();
                } else if (line.toUpperCase().includes('BY') || line.toUpperCase().includes('AUTHOR:')) {
                    author = line.replace(/by|author:/i, '').trim();
                }
            }
        });

        // Group elements into scenes
        const scenes = this.groupIntoScenes(elements);
        
        return {
            title,
            author,
            scenes,
            characters
        };
    }

    private static parseLine(line: string, lineNumber: number): ScreenplayElement | null {
        if (!line.trim()) return null;

        // Scene heading
        if (this.SCENE_HEADING_REGEX.test(line)) {
            return {
                type: 'scene_heading',
                content: line,
                lineNumber
            };
        }

        // Transition
        if (this.TRANSITION_REGEX.test(line)) {
            return {
                type: 'transition',
                content: line,
                lineNumber
            };
        }

        // Character name (all caps, centered-ish)
        if (this.CHARACTER_REGEX.test(line) && line.length < 30) {
            return {
                type: 'character',
                content: line,
                lineNumber
            };
        }

        // Parenthetical
        if (this.PARENTHETICAL_REGEX.test(line)) {
            return {
                type: 'parenthetical',
                content: line,
                lineNumber
            };
        }

        // Shot (usually all caps and short)
        if (line === line.toUpperCase() && line.length < 50 && 
            (line.includes('SHOT') || line.includes('ANGLE') || line.includes('VIEW'))) {
            return {
                type: 'shot',
                content: line,
                lineNumber
            };
        }

        // Default to action or dialogue based on context
        // This is a simplified heuristic - in practice, you'd need more context
        if (line.length > 0) {
            return {
                type: line.charAt(0) === line.charAt(0).toLowerCase() ? 'dialogue' : 'action',
                content: line,
                lineNumber
            };
        }

        return null;
    }

    private static groupIntoScenes(elements: ScreenplayElement[]): ParsedScene[] {
        const scenes: ParsedScene[] = [];
        let currentScene: ParsedScene | null = null;
        let sceneNumber = 1;

        for (const element of elements) {
            if (element.type === 'scene_heading') {
                // Start new scene
                if (currentScene) {
                    scenes.push(currentScene);
                }

                const { location, timeOfDay } = this.parseSceneHeading(element.content);
                
                currentScene = {
                    sceneNumber: sceneNumber++,
                    heading: element.content,
                    location,
                    timeOfDay,
                    elements: [element],
                    characters: [],
                    dialogue: [],
                    action: []
                };
            } else if (currentScene) {
                currentScene.elements.push(element);
                
                // Collect characters and dialogue
                if (element.type === 'character') {
                    if (!currentScene.characters.includes(element.content)) {
                        currentScene.characters.push(element.content);
                    }
                } else if (element.type === 'dialogue') {
                    // Find the last character to attribute this dialogue to
                    const lastCharacterIndex = currentScene.elements
                        .slice(0, -1)
                        .reverse()
                        .findIndex(el => el.type === 'character');
                    
                    if (lastCharacterIndex !== -1) {
                        const characterElement = currentScene.elements[
                            currentScene.elements.length - 2 - lastCharacterIndex
                        ];
                        
                        currentScene.dialogue.push({
                            character: characterElement.content,
                            text: element.content
                        });
                    }
                } else if (element.type === 'action') {
                    currentScene.action.push(element.content);
                }
            }
        }

        // Add the last scene
        if (currentScene) {
            scenes.push(currentScene);
        }

        return scenes;
    }

    private static parseSceneHeading(heading: string): { location: string; timeOfDay: string } {
        // Parse "INT. LOCATION - TIME" or "EXT. LOCATION - TIME"
        const match = heading.match(/^(INT\.|EXT\.)\s*(.+?)\s*-\s*(.+)$/i);
        
        if (match) {
            return {
                location: match[2].trim(),
                timeOfDay: match[3].trim()
            };
        }

        // Fallback parsing
        const parts = heading.split('-');
        return {
            location: parts[0]?.replace(/^(INT\.|EXT\.)/i, '').trim() || heading,
            timeOfDay: parts[1]?.trim() || 'DAY'
        };
    }

    static convertSceneToPromptData(scene: ParsedScene) {
        return {
            scene_title: scene.heading,
            environment: `${scene.location} - ${scene.timeOfDay}`,
            action: scene.action.join(' '),
            dialogue_entries: scene.dialogue.map((d, index) => ({
                id: `${scene.sceneNumber}-${index}`,
                subject_id: d.character,
                subject_name: d.character,
                dialogue: d.text,
                tone: 'neutral'
            })),
            scene_subjects: scene.characters
        };
    }

    static exportToFountain(screenplay: ParsedScreenplay): string {
        let output = '';
        
        if (screenplay.title) {
            output += `Title: ${screenplay.title}\n`;
        }
        if (screenplay.author) {
            output += `Author: ${screenplay.author}\n`;
        }
        
        output += '\n';
        
        for (const scene of screenplay.scenes) {
            output += scene.heading + '\n\n';
            
            for (const element of scene.elements) {
                if (element.type === 'scene_heading') continue;
                
                switch (element.type) {
                    case 'character':
                        output += element.content.toUpperCase() + '\n';
                        break;
                    case 'dialogue':
                        output += element.content + '\n';
                        break;
                    case 'parenthetical':
                        output += element.content + '\n';
                        break;
                    case 'action':
                        output += element.content + '\n';
                        break;
                    case 'transition':
                        output += element.content.toUpperCase() + '\n';
                        break;
                    case 'shot':
                        output += element.content + '\n';
                        break;
                }
            }
            
            output += '\n';
        }
        
        return output;
    }
}
