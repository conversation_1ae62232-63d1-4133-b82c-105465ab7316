import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    Play, 
    Pause, 
    Square, 
    Mic, 
    Download, 
    Upload, 
    Volume2, 
    Settings,
    Waveform
} from 'lucide-react';

interface VocalSettings {
    rate: number;        // 0.1 to 2.0
    pitch: number;       // 0.0 to 2.0
    volume: number;      // 0.0 to 1.0
    voice: string;       // voice identifier
    emphasis: string;    // none, strong, moderate
    pauseLength: number; // 0 to 5 seconds
}

interface VocalPerformanceControlsProps {
    text: string;
    onSettingsChange?: (settings: VocalSettings) => void;
    onAudioGenerated?: (audioBlob: Blob) => void;
}

export const VocalPerformanceControls: React.FC<VocalPerformanceControlsProps> = ({
    text,
    onSettingsChange,
    onAudioGenerated
}) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const [isRecording, setIsRecording] = useState(false);
    const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
    const [settings, setSettings] = useState<VocalSettings>({
        rate: 1.0,
        pitch: 1.0,
        volume: 0.8,
        voice: '',
        emphasis: 'none',
        pauseLength: 0.5
    });

    const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const audioChunksRef = useRef<Blob[]>([]);

    useEffect(() => {
        // Load available voices
        const loadVoices = () => {
            const voices = speechSynthesis.getVoices();
            setAvailableVoices(voices);
            
            // Set default voice
            if (voices.length > 0 && !settings.voice) {
                const defaultVoice = voices.find(voice => voice.default) || voices[0];
                setSettings(prev => ({ ...prev, voice: defaultVoice.name }));
            }
        };

        loadVoices();
        speechSynthesis.addEventListener('voiceschanged', loadVoices);

        return () => {
            speechSynthesis.removeEventListener('voiceschanged', loadVoices);
        };
    }, []);

    useEffect(() => {
        onSettingsChange?.(settings);
    }, [settings, onSettingsChange]);

    const handlePlay = () => {
        if (!text) return;

        if (isPlaying) {
            speechSynthesis.cancel();
            setIsPlaying(false);
            return;
        }

        const utterance = new SpeechSynthesisUtterance(text);
        utteranceRef.current = utterance;

        // Apply settings
        utterance.rate = settings.rate;
        utterance.pitch = settings.pitch;
        utterance.volume = settings.volume;

        // Find and set voice
        const selectedVoice = availableVoices.find(voice => voice.name === settings.voice);
        if (selectedVoice) {
            utterance.voice = selectedVoice;
        }

        utterance.onstart = () => setIsPlaying(true);
        utterance.onend = () => setIsPlaying(false);
        utterance.onerror = () => setIsPlaying(false);

        speechSynthesis.speak(utterance);
    };

    const handleStop = () => {
        speechSynthesis.cancel();
        setIsPlaying(false);
    };

    const handleRecord = async () => {
        if (isRecording) {
            // Stop recording
            if (mediaRecorderRef.current) {
                mediaRecorderRef.current.stop();
            }
            return;
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            const mediaRecorder = new MediaRecorder(stream);
            mediaRecorderRef.current = mediaRecorder;
            audioChunksRef.current = [];

            mediaRecorder.ondataavailable = (event) => {
                audioChunksRef.current.push(event.data);
            };

            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
                onAudioGenerated?.(audioBlob);
                
                // Stop all tracks
                stream.getTracks().forEach(track => track.stop());
                setIsRecording(false);
            };

            mediaRecorder.start();
            setIsRecording(true);
        } catch (error) {
            console.error('Error accessing microphone:', error);
        }
    };

    const handleDownloadAudio = () => {
        if (!text) return;

        // Create a temporary utterance for audio generation
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = settings.rate;
        utterance.pitch = settings.pitch;
        utterance.volume = settings.volume;

        const selectedVoice = availableVoices.find(voice => voice.name === settings.voice);
        if (selectedVoice) {
            utterance.voice = selectedVoice;
        }

        // Note: Web Speech API doesn't provide direct audio file generation
        // This would typically require a server-side TTS service
        console.log('Audio download would require server-side TTS service');
    };

    const updateSetting = <K extends keyof VocalSettings>(
        key: K, 
        value: VocalSettings[K]
    ) => {
        setSettings(prev => ({ ...prev, [key]: value }));
    };

    const getVoicesByLanguage = () => {
        const grouped = availableVoices.reduce((acc, voice) => {
            const lang = voice.lang.split('-')[0];
            if (!acc[lang]) acc[lang] = [];
            acc[lang].push(voice);
            return acc;
        }, {} as Record<string, SpeechSynthesisVoice[]>);
        
        return grouped;
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5" />
                    Vocal Performance Controls
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Playback Controls */}
                <div className="flex items-center gap-2">
                    <Button
                        onClick={handlePlay}
                        disabled={!text}
                        variant={isPlaying ? "secondary" : "default"}
                    >
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        {isPlaying ? 'Pause' : 'Play'}
                    </Button>
                    
                    <Button
                        onClick={handleStop}
                        disabled={!isPlaying}
                        variant="outline"
                    >
                        <Square className="h-4 w-4" />
                        Stop
                    </Button>

                    <Separator orientation="vertical" className="h-6" />

                    <Button
                        onClick={handleRecord}
                        variant={isRecording ? "destructive" : "outline"}
                    >
                        <Mic className="h-4 w-4" />
                        {isRecording ? 'Stop Recording' : 'Record'}
                    </Button>

                    <Button
                        onClick={handleDownloadAudio}
                        disabled={!text}
                        variant="outline"
                    >
                        <Download className="h-4 w-4" />
                        Export
                    </Button>
                </div>

                {/* Voice Selection */}
                <div className="space-y-2">
                    <Label>Voice</Label>
                    <Select
                        value={settings.voice}
                        onValueChange={(value) => updateSetting('voice', value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select a voice" />
                        </SelectTrigger>
                        <SelectContent>
                            {Object.entries(getVoicesByLanguage()).map(([lang, voices]) => (
                                <div key={lang}>
                                    <div className="px-2 py-1 text-sm font-medium text-muted-foreground">
                                        {lang.toUpperCase()}
                                    </div>
                                    {voices.map(voice => (
                                        <SelectItem key={voice.name} value={voice.name}>
                                            <div className="flex items-center gap-2">
                                                <span>{voice.name}</span>
                                                {voice.default && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        Default
                                                    </Badge>
                                                )}
                                            </div>
                                        </SelectItem>
                                    ))}
                                </div>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                {/* Performance Settings */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Rate Control */}
                    <div className="space-y-2">
                        <Label>Speech Rate: {settings.rate.toFixed(1)}x</Label>
                        <Slider
                            value={[settings.rate]}
                            onValueChange={([value]) => updateSetting('rate', value)}
                            min={0.1}
                            max={2.0}
                            step={0.1}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Slow</span>
                            <span>Normal</span>
                            <span>Fast</span>
                        </div>
                    </div>

                    {/* Pitch Control */}
                    <div className="space-y-2">
                        <Label>Pitch: {settings.pitch.toFixed(1)}</Label>
                        <Slider
                            value={[settings.pitch]}
                            onValueChange={([value]) => updateSetting('pitch', value)}
                            min={0.0}
                            max={2.0}
                            step={0.1}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Low</span>
                            <span>Normal</span>
                            <span>High</span>
                        </div>
                    </div>

                    {/* Volume Control */}
                    <div className="space-y-2">
                        <Label>Volume: {Math.round(settings.volume * 100)}%</Label>
                        <Slider
                            value={[settings.volume]}
                            onValueChange={([value]) => updateSetting('volume', value)}
                            min={0.0}
                            max={1.0}
                            step={0.1}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Quiet</span>
                            <span>Normal</span>
                            <span>Loud</span>
                        </div>
                    </div>

                    {/* Pause Length */}
                    <div className="space-y-2">
                        <Label>Pause Length: {settings.pauseLength}s</Label>
                        <Slider
                            value={[settings.pauseLength]}
                            onValueChange={([value]) => updateSetting('pauseLength', value)}
                            min={0}
                            max={5}
                            step={0.1}
                            className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <span>None</span>
                            <span>Normal</span>
                            <span>Long</span>
                        </div>
                    </div>
                </div>

                {/* Emphasis Control */}
                <div className="space-y-2">
                    <Label>Emphasis</Label>
                    <Select
                        value={settings.emphasis}
                        onValueChange={(value) => updateSetting('emphasis', value)}
                    >
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="moderate">Moderate</SelectItem>
                            <SelectItem value="strong">Strong</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {/* Status */}
                {text && (
                    <div className="p-3 bg-muted rounded-lg">
                        <div className="flex items-center gap-2 text-sm">
                            <Waveform className="h-4 w-4" />
                            <span>Ready to synthesize: {text.length} characters</span>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
