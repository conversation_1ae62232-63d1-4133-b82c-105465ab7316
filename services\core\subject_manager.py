from typing import Optional, List
from uuid import UUID
from datetime import datetime
from core.subject import Subject
from core.project import Project
from solar.access import User, authenticated

@authenticated
def create_subject(
    user: User,
    project_id: UUID,
    name: str,
    description: str,
    character_reference_url: Optional[str] = None,
    is_primary_character: bool = False
) -> Subject:
    """Create a new subject for a project."""
    # Verify the user owns the project
    project_results = Project.sql(
        "SELECT * FROM projects WHERE id = %(project_id)s AND user_id = %(user_id)s",
        {"project_id": project_id, "user_id": user.id}
    )
    
    if not project_results:
        raise ValueError("Project not found or not owned by user")
    
    # Check subject limit (max 3 per project)
    existing_subjects = get_project_subjects(user, project_id)
    if len(existing_subjects) >= 3:
        raise ValueError("Maximum of 3 subjects allowed per project")
    
    subject = Subject(
        project_id=project_id,
        name=name,
        description=description,
        character_reference_url=character_reference_url,
        is_primary_character=is_primary_character,
        last_updated=datetime.now()
    )
    subject.sync()
    return subject

@authenticated
def get_project_subjects(user: User, project_id: UUID) -> List[Subject]:
    """Get all subjects for a project owned by the user."""
    # Verify user owns the project
    project_results = Project.sql(
        "SELECT * FROM projects WHERE id = %(project_id)s AND user_id = %(user_id)s",
        {"project_id": project_id, "user_id": user.id}
    )
    
    if not project_results:
        return []
    
    results = Subject.sql(
        "SELECT * FROM subjects WHERE project_id = %(project_id)s ORDER BY is_primary_character DESC, created_at ASC",
        {"project_id": project_id}
    )
    return [Subject(**result) for result in results]

@authenticated
def update_subject(
    user: User,
    subject_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    character_reference_url: Optional[str] = None,
    is_primary_character: Optional[bool] = None
) -> Subject:
    """Update an existing subject."""
    # Get the subject and verify ownership through project
    subject_results = Subject.sql(
        "SELECT s.* FROM subjects s JOIN projects p ON s.project_id = p.id WHERE s.id = %(subject_id)s AND p.user_id = %(user_id)s",
        {"subject_id": subject_id, "user_id": user.id}
    )
    
    if not subject_results:
        raise ValueError("Subject not found or not owned by user")
    
    subject = Subject(**subject_results[0])
    
    if name is not None:
        subject.name = name
    if description is not None:
        subject.description = description
    if character_reference_url is not None:
        subject.character_reference_url = character_reference_url
    if is_primary_character is not None:
        subject.is_primary_character = is_primary_character
    
    subject.last_updated = datetime.now()
    subject.sync()
    return subject

@authenticated
def delete_subject(user: User, subject_id: UUID) -> bool:
    """Delete a subject."""
    # Verify ownership through project
    subject_results = Subject.sql(
        "SELECT s.* FROM subjects s JOIN projects p ON s.project_id = p.id WHERE s.id = %(subject_id)s AND p.user_id = %(user_id)s",
        {"subject_id": subject_id, "user_id": user.id}
    )
    
    if not subject_results:
        return False
    
    # Remove subject from any scenes that reference it
    Scene.sql(
        "UPDATE scenes SET scene_subjects = array_remove(scene_subjects, %(subject_id)s) WHERE %(subject_id)s = ANY(scene_subjects)",
        {"subject_id": str(subject_id)}
    )
    
    # Delete the subject
    Subject.sql(
        "DELETE FROM subjects WHERE id = %(subject_id)s",
        {"subject_id": subject_id}
    )
    
    return True