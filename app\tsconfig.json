{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noEmitOnError": false, "skipLibCheck": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "useUnknownInCatchVariables": false, "alwaysStrict": false, "allowJs": true, "checkJs": false, "allowSyntheticDefaultImports": true}}