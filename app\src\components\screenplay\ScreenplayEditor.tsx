import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
    Upload, 
    Download, 
    FileText, 
    Play, 
    Eye, 
    Import,
    Film,
    Users,
    MapPin
} from 'lucide-react';
import { ScreenplayParser, type ParsedScreenplay, type ParsedScene } from '@/utils/screenplayParser';

interface ScreenplayEditorProps {
    onSceneSelect?: (scene: ParsedScene) => void;
    onScreenplayChange?: (screenplay: ParsedScreenplay) => void;
}

export const ScreenplayEditor: React.FC<ScreenplayEditorProps> = ({
    onSceneSelect,
    onScreenplayChange
}) => {
    const [screenplayText, setScreenplayText] = useState('');
    const [parsedScreenplay, setParsedScreenplay] = useState<ParsedScreenplay | null>(null);
    const [selectedScene, setSelectedScene] = useState<ParsedScene | null>(null);
    const [isPreviewOpen, setIsPreviewOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleTextChange = (text: string) => {
        setScreenplayText(text);
        
        if (text.trim()) {
            try {
                const parsed = ScreenplayParser.parse(text);
                setParsedScreenplay(parsed);
                onScreenplayChange?.(parsed);
            } catch (error) {
                console.error('Error parsing screenplay:', error);
            }
        } else {
            setParsedScreenplay(null);
        }
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const text = e.target?.result as string;
            handleTextChange(text);
        };
        reader.readAsText(file);
    };

    const handleDownload = () => {
        if (!parsedScreenplay) return;

        const fountainText = ScreenplayParser.exportToFountain(parsedScreenplay);
        const blob = new Blob([fountainText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${parsedScreenplay.title || 'screenplay'}.fountain`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const handleSceneSelect = (scene: ParsedScene) => {
        setSelectedScene(scene);
        onSceneSelect?.(scene);
    };

    const getScenePreview = (scene: ParsedScene) => {
        const actionText = scene.action.slice(0, 2).join(' ');
        const dialogueCount = scene.dialogue.length;
        const characterCount = scene.characters.length;
        
        return {
            actionPreview: actionText.length > 100 ? actionText.substring(0, 100) + '...' : actionText,
            dialogueCount,
            characterCount
        };
    };

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Screenplay Integration
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <input
                            ref={fileInputRef}
                            type="file"
                            accept=".txt,.fountain,.fdx"
                            onChange={handleFileUpload}
                            className="hidden"
                        />
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => fileInputRef.current?.click()}
                        >
                            <Upload className="h-4 w-4 mr-2" />
                            Import
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleDownload}
                            disabled={!parsedScreenplay}
                        >
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                        <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
                            <DialogTrigger asChild>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={!parsedScreenplay}
                                >
                                    <Eye className="h-4 w-4 mr-2" />
                                    Preview
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                                <DialogHeader>
                                    <DialogTitle>Screenplay Preview</DialogTitle>
                                </DialogHeader>
                                {parsedScreenplay && (
                                    <div className="space-y-4">
                                        <div className="text-center border-b pb-4">
                                            <h2 className="text-2xl font-bold">
                                                {parsedScreenplay.title || 'Untitled Screenplay'}
                                            </h2>
                                            {parsedScreenplay.author && (
                                                <p className="text-muted-foreground">
                                                    by {parsedScreenplay.author}
                                                </p>
                                            )}
                                        </div>
                                        <div className="space-y-6">
                                            {parsedScreenplay.scenes.map((scene) => (
                                                <div key={scene.sceneNumber} className="border-l-4 border-primary pl-4">
                                                    <h3 className="font-bold text-lg">{scene.heading}</h3>
                                                    <div className="mt-2 space-y-2">
                                                        {scene.elements.map((element, index) => (
                                                            <div key={index} className={`
                                                                ${element.type === 'character' ? 'font-bold text-center' : ''}
                                                                ${element.type === 'dialogue' ? 'ml-8 mr-8' : ''}
                                                                ${element.type === 'parenthetical' ? 'ml-12 mr-12 text-sm italic' : ''}
                                                                ${element.type === 'action' ? 'my-2' : ''}
                                                                ${element.type === 'transition' ? 'text-right font-bold' : ''}
                                                            `}>
                                                                {element.content}
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <Tabs defaultValue="editor" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="editor">Editor</TabsTrigger>
                        <TabsTrigger value="scenes">Scenes</TabsTrigger>
                        <TabsTrigger value="characters">Characters</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="editor" className="space-y-4">
                        <div>
                            <Label htmlFor="screenplay-text">Screenplay Text</Label>
                            <Textarea
                                id="screenplay-text"
                                placeholder="Paste your screenplay here or import a file...

Example format:
FADE IN:

EXT. CITY STREET - DAY

A bustling street filled with people and cars.

DETECTIVE MORGAN
(into phone)
We've got a situation.

The detective looks around nervously."
                                value={screenplayText}
                                onChange={(e) => handleTextChange(e.target.value)}
                                rows={20}
                                className="font-mono text-sm"
                            />
                        </div>
                        
                        {parsedScreenplay && (
                            <div className="grid grid-cols-3 gap-4 p-4 bg-muted rounded-lg">
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{parsedScreenplay.scenes.length}</div>
                                    <div className="text-sm text-muted-foreground">Scenes</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{parsedScreenplay.characters.size}</div>
                                    <div className="text-sm text-muted-foreground">Characters</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">
                                        {parsedScreenplay.scenes.reduce((acc, scene) => acc + scene.dialogue.length, 0)}
                                    </div>
                                    <div className="text-sm text-muted-foreground">Dialogue Lines</div>
                                </div>
                            </div>
                        )}
                    </TabsContent>
                    
                    <TabsContent value="scenes" className="space-y-4">
                        {parsedScreenplay ? (
                            <div className="space-y-3">
                                {parsedScreenplay.scenes.map((scene) => {
                                    const preview = getScenePreview(scene);
                                    return (
                                        <Card 
                                            key={scene.sceneNumber} 
                                            className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                                                selectedScene?.sceneNumber === scene.sceneNumber ? 'ring-2 ring-primary' : ''
                                            }`}
                                            onClick={() => handleSceneSelect(scene)}
                                        >
                                            <CardContent className="p-4">
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-2">
                                                            <Badge variant="outline">Scene {scene.sceneNumber}</Badge>
                                                            <h3 className="font-semibold">{scene.heading}</h3>
                                                        </div>
                                                        
                                                        {preview.actionPreview && (
                                                            <p className="text-sm text-muted-foreground mb-2">
                                                                {preview.actionPreview}
                                                            </p>
                                                        )}
                                                        
                                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                            <div className="flex items-center gap-1">
                                                                <Users className="h-3 w-3" />
                                                                {preview.characterCount} characters
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <Film className="h-3 w-3" />
                                                                {preview.dialogueCount} dialogue lines
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <MapPin className="h-3 w-3" />
                                                                {scene.location}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleSceneSelect(scene);
                                                        }}
                                                    >
                                                        <Import className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-muted-foreground">
                                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>Import or write a screenplay to see scenes</p>
                            </div>
                        )}
                    </TabsContent>
                    
                    <TabsContent value="characters" className="space-y-4">
                        {parsedScreenplay && parsedScreenplay.characters.size > 0 ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {Array.from(parsedScreenplay.characters).map((character) => (
                                    <Badge key={character} variant="secondary" className="justify-center p-2">
                                        {character}
                                    </Badge>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-muted-foreground">
                                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No characters found in screenplay</p>
                            </div>
                        )}
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
};
