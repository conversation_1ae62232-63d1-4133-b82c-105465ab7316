import React, { createContext, useContext, ReactNode, useEffect, useState } from 'react';
import { useGoogleAuth } from '@/hooks/use-google-auth';
import { Client } from '@hey-api/client-fetch';

interface GoogleUserDetails {
    id: string;
    email: string;
    name: string;
    picture?: string;
    verified_email: boolean;
}

interface GoogleAuthContextType {
    isLoggedIn: boolean;
    userDetails: GoogleUserDetails | null;
    authLoading: boolean;
    token: string | null;
    logout: () => void;
    login: () => void;
    clientReady: boolean;
    appName?: string;
}

const GoogleAuthContext = createContext<GoogleAuthContextType | null>(null);

export const useGoogleAuthContext = () => {
    const context = useContext(GoogleAuthContext);
    if (!context) {
        throw new Error('useGoogleAuthContext must be used within a GoogleAuthProvider');
    }
    return context;
};

export const GoogleSignedIn: React.FC<{ children: ReactNode }> = ({ children }) => {
    const { isLoggedIn, clientReady, token } = useGoogleAuthContext();
    
    if (!clientReady || !isLoggedIn || !token) {
        return null;
    }
    
    return <>{children}</>;
};

export const GoogleSignedOut: React.FC<{ children?: ReactNode }> = ({ children }) => {
    const { isLoggedIn, clientReady } = useGoogleAuthContext();
    
    if (!clientReady) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-background">
                <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-4 border-primary border-t-transparent"></div>
                    <p className="text-muted-foreground">Authenticating with Google...</p>
                </div>
            </div>
        );
    }
    
    if (isLoggedIn) {
        return null;
    }
    
    return <>{children}</>;
};

interface GoogleAuthProviderProps {
    children: ReactNode;
    client: Client;
    appName?: string;
}

export const GoogleAuthProvider: React.FC<GoogleAuthProviderProps> = ({ 
    children, 
    client, 
    appName 
}) => {
    const [authToken, setAuthToken] = useState<string | null>(null);
    const {
        isLoggedIn,
        userDetails,
        authLoading,
        login,
        logout,
        token
    } = useGoogleAuth();

    useEffect(() => {
        if (isLoggedIn && token) {
            const interceptor = client.interceptors.request.use((request) => {
                request.headers.set("Authorization", `Bearer ${token}`);
                return request;
            });
            
            setAuthToken(token);
            
            return () => {
                client.interceptors.request.eject(interceptor);
            };
        }
    }, [isLoggedIn, token, client]);

    const clientReady = !authLoading && (isLoggedIn ? !!token : true);

    return (
        <GoogleAuthContext.Provider value={{ 
            isLoggedIn, 
            userDetails, 
            authLoading, 
            token: authToken, 
            logout, 
            login, 
            clientReady, 
            appName 
        }}>
            {children}
        </GoogleAuthContext.Provider>
    );
};
