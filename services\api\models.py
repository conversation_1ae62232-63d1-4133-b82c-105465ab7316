# Auto-generated by <PERSON><PERSON><PERSON>
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union, Literal, Annotated, Tuple, Set, Any

from datetime import datetime, date, time, timedelta
from uuid import UUID
import uuid

class TokenExchangeRequest(BaseModel):
    client_id: str
    grant_type: str
    code: Optional[str] = None
    code_verifier: Optional[str] = None

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 3600

class TokenValidationRequest(BaseModel):
    token: str
    
class LogoutResponse(BaseModel):
    success: bool = True

# Import user-defined models that we need for input/response models
from core.project import Project
from core.subject import Subject
from core.scene import Scene
from core.user import User

class BodyPromptGeneratorCreateScene(BaseModel):
  project_id: UUID
  scene_number: int
  scene_title: Optional[str] = None
  action: Optional[str] = None
  environment: Optional[str] = None
  scene_subjects: Optional[List[str]] = None
  dialogue_entries: Optional[List[Dict]] = None
  composition: Optional[str] = None
  angle: Optional[str] = None
  camera_movements: Optional[List[str]] = None
  visual_aesthetic: Optional[str] = None
  lighting_design: Optional[str] = None
  sound_design: Optional[str] = None
  negative_prompt: Optional[str] = None
  seed: Optional[int] = None
  screenplay_content: Optional[str] = None

CreateSceneOutputSchema = Scene
class BodyPromptGeneratorGetScene(BaseModel):
  scene_id: UUID

GetSceneOutputSchema = Optional[Scene]
class BodyPromptGeneratorGetProjectScenes(BaseModel):
  project_id: UUID

GetProjectScenesOutputSchema = List[Scene]
class BodyPromptGeneratorUpdateScene(BaseModel):
  scene_id: UUID
  scene_title: Optional[str] = None
  action: Optional[str] = None
  environment: Optional[str] = None
  scene_subjects: Optional[List[str]] = None
  dialogue_entries: Optional[List[Dict]] = None
  composition: Optional[str] = None
  angle: Optional[str] = None
  camera_movements: Optional[List[str]] = None
  visual_aesthetic: Optional[str] = None
  lighting_design: Optional[str] = None
  sound_design: Optional[str] = None
  negative_prompt: Optional[str] = None
  seed: Optional[int] = None
  screenplay_content: Optional[str] = None

UpdateSceneOutputSchema = Scene
class BodyProjectManagerCreateProject(BaseModel):
  name: str
  description: Optional[str] = None

CreateProjectOutputSchema = Project
GetUserProjectsOutputSchema = List[Project]
class BodyProjectManagerGetProject(BaseModel):
  project_id: UUID

GetProjectOutputSchema = Optional[Project]
class BodyProjectManagerUpdateProject(BaseModel):
  project_id: UUID
  name: Optional[str] = None
  description: Optional[str] = None
  global_visual_aesthetic: Optional[str] = None
  global_lighting_design: Optional[str] = None

UpdateProjectOutputSchema = Project
class BodyProjectManagerDeleteProject(BaseModel):
  project_id: UUID

DeleteProjectOutputSchema = bool
class BodySubjectManagerCreateSubject(BaseModel):
  project_id: UUID
  name: str
  description: str
  character_reference_url: Optional[str] = None
  is_primary_character: bool

CreateSubjectOutputSchema = Subject
class BodySubjectManagerGetProjectSubjects(BaseModel):
  project_id: UUID

GetProjectSubjectsOutputSchema = List[Subject]
class BodySubjectManagerUpdateSubject(BaseModel):
  subject_id: UUID
  name: Optional[str] = None
  description: Optional[str] = None
  character_reference_url: Optional[str] = None
  is_primary_character: Optional[bool] = None

UpdateSubjectOutputSchema = Subject
class BodySubjectManagerDeleteSubject(BaseModel):
  subject_id: UUID

DeleteSubjectOutputSchema = bool
    