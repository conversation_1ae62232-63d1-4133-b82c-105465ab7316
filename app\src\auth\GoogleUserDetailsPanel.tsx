import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useGoogleAuthContext } from './GoogleAuthProvider';
import { LogOut, User, Mail, Shield } from 'lucide-react';

export const GoogleUserDetailsPanel: React.FC = () => {
    const { userDetails, logout, appName } = useGoogleAuthContext();

    if (!userDetails) {
        return null;
    }

    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(part => part.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <Card className="w-full max-w-md">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Account Details
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16">
                        <AvatarImage src={userDetails.picture} alt={userDetails.name} />
                        <AvatarFallback className="text-lg">
                            {getInitials(userDetails.name)}
                        </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg">{userDetails.name}</h3>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Mail className="h-4 w-4" />
                            {userDetails.email}
                        </div>
                        {userDetails.verified_email && (
                            <div className="flex items-center gap-2 mt-1">
                                <Badge variant="secondary" className="text-xs">
                                    <Shield className="h-3 w-3 mr-1" />
                                    Verified
                                </Badge>
                            </div>
                        )}
                    </div>
                </div>

                <div className="pt-4 border-t">
                    <div className="text-sm text-muted-foreground mb-2">
                        Signed in to {appName || 'Veo 3 Prompt Architect'}
                    </div>
                    <Button
                        onClick={handleLogout}
                        variant="outline"
                        className="w-full"
                        size="sm"
                    >
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign Out
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
};
