{"name": "vite-app-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.5", "@radix-ui/react-navigation-menu": "^1.2.4", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@react-three/drei": "^10.0.4", "@react-three/fiber": "^9.1.0", "@tanstack/react-query": "^4.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.0.3", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.10", "input-otp": "^1.4.2", "knip": "^5.50.4", "lodash": "^4.17.21", "loglevel": "^1.9.2", "lucide-react": "^0.474.0", "mathjs": "^12.4.0", "next-themes": "^0.4.4", "papaparse": "^5.4.1", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-font": "^1.2.1", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-leaflet": "^5.0.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.1.3", "recharts": "^2.15.1", "sonner": "^1.7.2", "strip-ansi": "^7.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.174.0", "uuid": "^10.0.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.1", "google-auth-library": "^9.14.1", "@google-cloud/oauth2": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@hey-api/client-fetch": "^0.7.2", "@hey-api/openapi-ts": "^0.63.0", "@types/lodash": "^4.14.202", "@types/loglevel": "^1.6.3", "@types/node": "^22.10.10", "@types/papaparse": "^5.3.14", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/three": "^0.174.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}, "packageManager": "pnpm@9.15.0+sha512.76e2379760a4328ec4415815bcd6628dee727af3779aaa4c914e3944156c4299921a89f976381ee107d41f12cfa4b66681ca9c718f0668fa0831ed4c6d8ba56c"}