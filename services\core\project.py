from solar import Table, ColumnDetails
from typing import Optional
from datetime import datetime
import uuid

class Project(Table):
    __tablename__ = "projects"
    
    id: uuid.UUID = ColumnDetails(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID  # Reference to authenticated user
    
    name: str
    description: Optional[str] = None
    
    # Global project settings that apply to all scenes
    global_visual_aesthetic: Optional[str] = None
    global_lighting_design: Optional[str] = None
    
    created_at: datetime = ColumnDetails(default_factory=datetime.now)
    last_updated: datetime = ColumnDetails(default_factory=datetime.now)