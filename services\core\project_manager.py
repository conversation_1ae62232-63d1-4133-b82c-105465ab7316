from typing import Optional, List
from uuid import UUID
from datetime import datetime
from core.project import Project
from core.subject import Subject
from core.scene import Scene
from solar.access import User, authenticated

@authenticated
def create_project(user: User, name: str, description: Optional[str] = None) -> Project:
    """Create a new project for the authenticated user."""
    project = Project(
        user_id=user.id,
        name=name,
        description=description,
        last_updated=datetime.now()
    )
    project.sync()
    return project

@authenticated
def get_user_projects(user: User) -> List[Project]:
    """Get all projects for the authenticated user."""
    results = Project.sql(
        "SELECT * FROM projects WHERE user_id = %(user_id)s ORDER BY last_updated DESC",
        {"user_id": user.id}
    )
    return [Project(**result) for result in results]

@authenticated
def get_project(user: User, project_id: UUID) -> Optional[Project]:
    """Get a specific project owned by the authenticated user."""
    results = Project.sql(
        "SELECT * FROM projects WHERE id = %(project_id)s AND user_id = %(user_id)s",
        {"project_id": project_id, "user_id": user.id}
    )
    if results:
        return Project(**results[0])
    return None

@authenticated
def update_project(
    user: User,
    project_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    global_visual_aesthetic: Optional[str] = None,
    global_lighting_design: Optional[str] = None
) -> Project:
    """Update an existing project."""
    project = get_project(user, project_id)
    if not project:
        raise ValueError(f"Project with ID {project_id} not found or not owned by user")
    
    if name is not None:
        project.name = name
    if description is not None:
        project.description = description
    if global_visual_aesthetic is not None:
        project.global_visual_aesthetic = global_visual_aesthetic
    if global_lighting_design is not None:
        project.global_lighting_design = global_lighting_design
    
    project.last_updated = datetime.now()
    project.sync()
    return project

@authenticated
def delete_project(user: User, project_id: UUID) -> bool:
    """Delete a project and all its associated scenes and subjects."""
    project = get_project(user, project_id)
    if not project:
        return False
    
    # Delete all scenes in the project
    Scene.sql(
        "DELETE FROM scenes WHERE project_id = %(project_id)s",
        {"project_id": project_id}
    )
    
    # Delete all subjects in the project
    Subject.sql(
        "DELETE FROM subjects WHERE project_id = %(project_id)s",
        {"project_id": project_id}
    )
    
    # Delete the project
    Project.sql(
        "DELETE FROM projects WHERE id = %(project_id)s",
        {"project_id": project_id}
    )
    
    return True