[project]
name = "service"
version = "0.1.0"
description = "Solar service template"
requires-python = ">=3.11"
dependencies = [
    "beautifulsoup4>=4.13.3",
    "boto3>=1.38.10",
    "browser-use>=0.1.40",
    "fastapi>=0.115.12",
    "h11>=0.16.0",
    "httpcore>=1.0.9",
    "httpx>=0.28.1",
    "loguru>=0.7.3",
    "psycopg>=3.2.6",
    "psycopg-pool>=3.2.6",
    "pydantic>=2.11.3",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.0",
    "python-multipart>=0.0.20",
    "requests>=2.32.3",
    "uvicorn>=0.34.1",
]
